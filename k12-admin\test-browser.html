<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>JSON上传测试页面</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .success {
            color: green;
        }
        .error {
            color: red;
        }
        .info {
            color: blue;
        }
        button {
            padding: 10px 20px;
            margin: 5px;
            cursor: pointer;
        }
        #log {
            background: #f5f5f5;
            padding: 10px;
            border-radius: 5px;
            white-space: pre-wrap;
            max-height: 400px;
            overflow-y: auto;
        }
    </style>
</head>
<body>
    <h1>JSON批量上传测试页面</h1>
    
    <div class="test-section">
        <h3>1. 创建测试JSON文件</h3>
        <button onclick="createTestJson()">创建测试JSON</button>
        <div id="json-content"></div>
    </div>
    
    <div class="test-section">
        <h3>2. 上传JSON文件</h3>
        <input type="file" id="jsonFile" accept=".json">
        <button onclick="validateJson()">验证JSON</button>
        <button onclick="uploadJson()">上传JSON</button>
    </div>
    
    <div class="test-section">
        <h3>3. 测试日志</h3>
        <button onclick="clearLog()">清空日志</button>
        <div id="log"></div>
    </div>

    <script>
        let testJsonData = null;
        
        function log(message, type = 'info') {
            const logDiv = document.getElementById('log');
            const timestamp = new Date().toLocaleTimeString();
            const className = type === 'error' ? 'error' : type === 'success' ? 'success' : 'info';
            logDiv.innerHTML += `<span class="${className}">[${timestamp}] ${message}</span>\n`;
            logDiv.scrollTop = logDiv.scrollHeight;
        }
        
        function clearLog() {
            document.getElementById('log').innerHTML = '';
        }
        
        function createTestJson() {
            testJsonData = [
                {
                    "file_path": "C:\\Users\\<USER>\\Desktop\\test1.pdf",
                    "title": "浏览器测试文件1",
                    "description": "这是浏览器测试文件1",
                    "category": "regular",
                    "grade": "一年级",
                    "subject": "数学",
                    "volume": "上册",
                    "section": "单元同步",
                    "tags": ["浏览器测试"],
                    "features": ["测试"],
                    "status": "active"
                }
            ];
            
            const jsonContent = document.getElementById('json-content');
            jsonContent.innerHTML = `<pre>${JSON.stringify(testJsonData, null, 2)}</pre>`;
            
            // 创建下载链接
            const blob = new Blob([JSON.stringify(testJsonData, null, 2)], { type: 'application/json' });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = 'test-upload.json';
            a.textContent = '下载测试JSON文件';
            jsonContent.appendChild(a);
            
            log('测试JSON文件已创建', 'success');
        }
        
        async function validateJson() {
            const fileInput = document.getElementById('jsonFile');
            if (!fileInput.files[0]) {
                log('请先选择JSON文件', 'error');
                return;
            }
            
            const formData = new FormData();
            formData.append('jsonFile', fileInput.files[0]);
            formData.append('validateOnly', 'true');
            
            try {
                log('开始验证JSON文件...');
                
                const response = await fetch('/api/files/batch/upload', {
                    method: 'POST',
                    body: formData
                });
                
                log(`验证请求状态: ${response.status}`);
                
                if (!response.ok) {
                    const errorText = await response.text();
                    log(`验证失败: ${response.status} - ${errorText}`, 'error');
                    return;
                }
                
                const result = await response.json();
                log(`验证结果: ${JSON.stringify(result, null, 2)}`, 'success');
                
            } catch (error) {
                log(`验证过程出错: ${error.message}`, 'error');
                console.error('验证错误:', error);
            }
        }
        
        async function uploadJson() {
            const fileInput = document.getElementById('jsonFile');
            if (!fileInput.files[0]) {
                log('请先选择JSON文件', 'error');
                return;
            }
            
            const formData = new FormData();
            formData.append('jsonFile', fileInput.files[0]);
            formData.append('validateOnly', 'false');
            
            try {
                log('开始上传JSON文件...');
                
                const response = await fetch('/api/files/batch/upload', {
                    method: 'POST',
                    body: formData
                });
                
                log(`上传请求状态: ${response.status}`);
                
                if (!response.ok) {
                    const errorText = await response.text();
                    log(`上传失败: ${response.status} - ${errorText}`, 'error');
                    return;
                }
                
                const result = await response.json();
                log(`上传结果: ${JSON.stringify(result, null, 2)}`, 'success');
                
                if (result.success) {
                    log(`上传成功! 成功: ${result.summary?.success || 0}, 失败: ${result.summary?.failed || 0}`, 'success');
                } else {
                    log(`上传失败: ${result.error}`, 'error');
                }
                
            } catch (error) {
                log(`上传过程出错: ${error.message}`, 'error');
                console.error('上传错误:', error);
            }
        }
        
        // 页面加载时的初始化
        window.onload = function() {
            log('JSON上传测试页面已加载');
            log('请先创建测试JSON文件，然后选择文件进行测试');
        };
    </script>
</body>
</html>
