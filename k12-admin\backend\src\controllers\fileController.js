// 文件管理控制器
const fileService = require('../services/fileService')
const uploadService = require('../services/uploadService')
const csvService = require('../services/csvService')
const multer = require('multer')
const path = require('path')
const fs = require('fs')
const Joi = require('joi')

// 配置multer用于文件上传
const storage = multer.diskStorage({
  destination: (req, file, cb) => {
    const uploadDir = path.join(__dirname, '../../uploads/temp')
    if (!fs.existsSync(uploadDir)) {
      fs.mkdirSync(uploadDir, { recursive: true })
    }
    cb(null, uploadDir)
  },
  filename: (req, file, cb) => {
    // 使用时间戳和随机数生成唯一文件名
    const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9)
    cb(null, file.fieldname + '-' + uniqueSuffix + path.extname(file.originalname))
  }
})

const upload = multer({
  storage: storage,
  limits: {
    fileSize: 50 * 1024 * 1024 // 50MB
  },
  fileFilter: (req, file, cb) => {
    const allowedTypes = [
      'application/pdf',
      'application/msword',
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
      'application/vnd.ms-powerpoint',
      'application/vnd.openxmlformats-officedocument.presentationml.presentation',
      'application/vnd.ms-excel',
      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
      'text/csv'
    ]

    if (allowedTypes.includes(file.mimetype)) {
      cb(null, true)
    } else {
      cb(new Error('不支持的文件类型'), false)
    }
  }
})

class FileController {
  // 获取文件列表
  async getFileList(req, res) {
    try {
      const schema = Joi.object({
        page: Joi.number().integer().min(1).default(1),
        pageSize: Joi.number().integer().min(1).max(100).default(20),
        grade: Joi.string().allow('').optional(),
        subject: Joi.string().allow('').optional(),
        volume: Joi.string().allow('').optional(),
        section: Joi.string().allow('').optional(),
        category: Joi.string().allow('').optional(),
        status: Joi.string().valid('active', 'inactive', '').default('active'),
        keyword: Joi.string().allow('').optional(),
        timeRange: Joi.string().allow('').optional(),  // 新增：时间范围类型
        startDate: Joi.string().allow('').optional(),  // 新增：开始日期
        endDate: Joi.string().allow('').optional(),    // 新增：结束日期
        sortBy: Joi.string().valid('created_time', 'download_count', 'view_count', 'sort_order').default('created_time'),
        sortOrder: Joi.string().valid('asc', 'desc').default('desc')
      })

      const { error, value } = schema.validate(req.query)
      if (error) {
        return res.status(400).json({
          success: false,
          error: error.details[0].message
        })
      }

      const result = await fileService.getFileList(value)
      res.json(result)
    } catch (error) {
      res.status(500).json({
        success: false,
        error: error.message
      })
    }
  }

  // 获取文件详情
  async getFileDetail(req, res) {
    try {
      const { id } = req.params
      const result = await fileService.getFileDetail(id)

      if (!result.success) {
        return res.status(404).json(result)
      }

      // 更新查看统计
      await uploadService.updateViewStats(id)

      res.json(result)
    } catch (error) {
      res.status(500).json({
        success: false,
        error: error.message
      })
    }
  }

  // 更新文件信息
  async updateFile(req, res) {
    try {
      const { id } = req.params
      const schema = Joi.object({
        title: Joi.string().required(),
        description: Joi.string().allow('').optional(),
        grade: Joi.string().optional(),
        subject: Joi.string().required(),
        volume: Joi.string().required(),
        section: Joi.string().required(),
        category: Joi.string().valid('regular', 'upgrade', '').default('regular'),
        tags: Joi.array().items(Joi.string()).optional(),
        ad_required_count: Joi.number().integer().min(1).optional(),
        sort_order: Joi.number().integer().optional(),
        features: Joi.array().items(Joi.string()).optional(),
        status: Joi.string().valid('active', 'inactive').optional(),
        // 添加统计字段支持
        download_count: Joi.number().integer().min(0).optional(),
        view_count: Joi.number().integer().min(0).optional()
      })

      const { error, value } = schema.validate(req.body)
      if (error) {
        return res.status(400).json({
          success: false,
          error: error.details[0].message
        })
      }

      const result = await fileService.updateFile(id, value)
      res.json(result)
    } catch (error) {
      res.status(500).json({
        success: false,
        error: error.message
      })
    }
  }

  // 删除文件
  async deleteFile(req, res) {
    try {
      const { id } = req.params
      const result = await fileService.deleteFile(id)
      res.json(result)
    } catch (error) {
      res.status(500).json({
        success: false,
        error: error.message
      })
    }
  }

  // 批量删除文件
  async batchDeleteFiles(req, res) {
    try {
      const schema = Joi.object({
        fileIds: Joi.array().items(Joi.string()).min(1).required()
      })

      const { error, value } = schema.validate(req.body)
      if (error) {
        return res.status(400).json({
          success: false,
          error: error.details[0].message
        })
      }

      const result = await fileService.batchDeleteFiles(value.fileIds)
      res.json(result)
    } catch (error) {
      res.status(500).json({
        success: false,
        error: error.message
      })
    }
  }

  // 批量更新文件状态
  async batchUpdateStatus(req, res) {
    try {
      const schema = Joi.object({
        fileIds: Joi.array().items(Joi.string()).min(1).required(),
        status: Joi.string().valid('active', 'inactive').required()
      })

      const { error, value } = schema.validate(req.body)
      if (error) {
        return res.status(400).json({
          success: false,
          error: error.details[0].message
        })
      }

      const result = await fileService.batchUpdateStatus(value.fileIds, value.status)
      res.json(result)
    } catch (error) {
      res.status(500).json({
        success: false,
        error: error.message
      })
    }
  }

  // 上传单个文件
  async uploadFile(req, res) {
    try {
      if (!req.file) {
        return res.status(400).json({
          success: false,
          error: '请选择要上传的文件'
        })
      }

      const schema = Joi.object({
        title: Joi.string().required(),
        description: Joi.string().allow('').optional(),
        grade: Joi.string().optional(),
        subject: Joi.string().required(),
        volume: Joi.string().required(),
        section: Joi.string().required(),
        category: Joi.string().valid('regular', 'upgrade').default('regular'),
        tags: Joi.string().optional(), // 逗号分隔的字符串
        ad_required_count: Joi.number().integer().min(1).default(1),
        sort_order: Joi.number().integer().default(0),
        features: Joi.string().optional() // 逗号分隔的字符串
      })

      const { error, value } = schema.validate(req.body)
      if (error) {
        return res.status(400).json({
          success: false,
          error: error.details[0].message
        })
      }

      // 处理数组字段
      if (value.tags) {
        value.tags = value.tags.split(',').map(tag => tag.trim()).filter(tag => tag)
      }
      if (value.features) {
        value.features = value.features.split(',').map(feature => feature.trim()).filter(feature => feature)
      }

      const result = await uploadService.uploadFile(req.file, value)
      res.json(result)
    } catch (error) {
      res.status(500).json({
        success: false,
        error: error.message
      })
    }
  }

  // 批量上传文件
  async batchUploadFiles(req, res) {
    try {
      if (!req.files || req.files.length === 0) {
        return res.status(400).json({
          success: false,
          error: '请选择要上传的文件'
        })
      }

      const schema = Joi.object({
        grade: Joi.string().optional(),
        subject: Joi.string().required(),
        volume: Joi.string().required(),
        section: Joi.string().required(),
        category: Joi.string().valid('regular', 'upgrade').default('regular'),
        tags: Joi.string().optional(),
        ad_required_count: Joi.number().integer().min(1).default(1),
        sort_order: Joi.number().integer().default(0),
        features: Joi.string().optional()
      })

      const { error, value } = schema.validate(req.body)
      if (error) {
        return res.status(400).json({
          success: false,
          error: error.details[0].message
        })
      }

      // 处理数组字段
      if (value.tags) {
        value.tags = value.tags.split(',').map(tag => tag.trim()).filter(tag => tag)
      }
      if (value.features) {
        value.features = value.features.split(',').map(feature => feature.trim()).filter(feature => feature)
      }

      const result = await uploadService.batchUploadFiles(req.files, value)
      res.json(result)
    } catch (error) {
      res.status(500).json({
        success: false,
        error: error.message
      })
    }
  }

  // 获取文件下载链接
  async getDownloadUrl(req, res) {
    try {
      const { id } = req.params
      console.log('获取下载链接请求:', { fileId: id })

      const result = await uploadService.getDownloadUrl(id)
      console.log('下载链接获取结果:', result)

      if (result.success) {
        // 更新下载统计
        try {
          await uploadService.updateDownloadStats(id)
        } catch (statsError) {
          console.warn('更新下载统计失败:', statsError)
          // 不影响下载链接返回
        }
      }

      res.json(result)
    } catch (error) {
      console.error('获取下载链接失败:', error)
      res.status(500).json({
        success: false,
        error: error.message
      })
    }
  }

  // 诊断文件URL问题
  async diagnoseFile(req, res) {
    try {
      const { id } = req.params

      // 获取文件信息
      const fileResult = await uploadService.filesCollection.doc(id).get()

      if (!fileResult.data.length) {
        return res.json({
          success: false,
          error: '文件记录不存在'
        })
      }

      const fileData = fileResult.data[0]

      const diagnosis = {
        fileId: id,
        title: fileData.title,
        file_url: fileData.file_url,
        url_format: fileData.file_url ? (fileData.file_url.startsWith('cloud://') ? 'cloud://' : 'other') : 'empty',
        url_length: fileData.file_url ? fileData.file_url.length : 0,
        created_time: fileData.created_time,
        file_size: fileData.file_size,
        file_type: fileData.file_type
      }

      // 测试临时URL获取
      if (fileData.file_url && fileData.file_url.startsWith('cloud://')) {
        try {
          const tempUrlResult = await uploadService.app.getTempFileURL({
            fileList: [{
              fileID: fileData.file_url,
              maxAge: 60 // 1分钟测试
            }]
          })

          diagnosis.temp_url_test = {
            success: tempUrlResult.fileList?.[0]?.code === 'SUCCESS',
            code: tempUrlResult.fileList?.[0]?.code,
            message: tempUrlResult.fileList?.[0]?.message || '无消息'
          }

          if (tempUrlResult.fileList?.[0]?.tempFileURL) {
            diagnosis.temp_url_sample = tempUrlResult.fileList[0].tempFileURL
          }
        } catch (error) {
          diagnosis.temp_url_test = {
            success: false,
            error: error.message
          }
        }
      }

      console.log('文件诊断结果:', diagnosis)

      res.json({
        success: true,
        data: diagnosis
      })
    } catch (error) {
      console.error('文件诊断失败:', error)
      res.status(500).json({
        success: false,
        error: error.message
      })
    }
  }

  // 获取文件统计信息
  async getFileStats(req, res) {
    try {
      const result = await fileService.getFileStats()
      res.json(result)
    } catch (error) {
      res.status(500).json({
        success: false,
        error: error.message
      })
    }
  }

  // 获取筛选选项
  async getFilterOptions(req, res) {
    try {
      const result = await fileService.getFilterOptions()
      res.json(result)
    } catch (error) {
      res.status(500).json({
        success: false,
        error: error.message
      })
    }
  }

  // 下载CSV模板
  async downloadCsvTemplate(req, res) {
    try {
      const tempPath = path.join(__dirname, '../../uploads/temp/template.csv')
      const result = await csvService.generateTemplate(tempPath)

      if (!result.success) {
        return res.status(500).json(result)
      }

      res.download(tempPath, 'files_upload_template.csv', (err) => {
        if (err) {
          console.error('下载模板失败:', err)
        }
        // 清理临时文件
        try {
          fs.unlinkSync(tempPath)
        } catch (cleanupError) {
          console.warn('清理模板文件失败:', cleanupError)
        }
      })
    } catch (error) {
      res.status(500).json({
        success: false,
        error: error.message
      })
    }
  }

  // CSV批量上传
  async csvBatchUpload(req, res) {
    try {
      if (!req.file) {
        return res.status(400).json({
          success: false,
          error: '请选择CSV文件'
        })
      }

      if (req.file.mimetype !== 'text/csv') {
        return res.status(400).json({
          success: false,
          error: '请上传CSV格式文件'
        })
      }

      const validateOnly = req.body.validateOnly === 'true'

      console.log('CSV上传请求参数:', {
        validateOnly,
        fileName: req.file.originalname,
        fileSize: req.file.size,
        filePath: req.file.path
      })

      let result
      if (validateOnly) {
        // 仅验证模式
        console.log('执行CSV验证模式')
        result = await csvService.validateCsvFile(req.file.path)
      } else {
        // 完整上传模式
        console.log('执行CSV完整上传模式')
        result = await csvService.processCsvUpload(req.file.path)
      }

      console.log('CSV处理结果:', result)

      // 清理临时文件
      try {
        fs.unlinkSync(req.file.path)
      } catch (cleanupError) {
        console.warn('清理CSV文件失败:', cleanupError)
      }

      res.json(result)
    } catch (error) {
      // 确保清理临时文件
      if (req.file && req.file.path) {
        try {
          fs.unlinkSync(req.file.path)
        } catch (cleanupError) {
          console.warn('清理CSV文件失败:', cleanupError)
        }
      }

      res.status(500).json({
        success: false,
        error: error.message
      })
    }
  }

  // 检查云存储配置
  async checkStorageConfig(req, res) {
    try {
      const configInfo = {
        environment: process.env.CLOUDBASE_ENV,
        storage_type: '腾讯云开发云存储',
        default_permission: '私有读写',
        access_method: 'getTempFileURL API',
        notes: [
          '腾讯云开发的云存储默认为私有权限',
          '文件不能直接通过HTTPS链接访问',
          '必须使用getTempFileURL API获取临时访问链接',
          '临时链接有时效性，默认1小时',
          '如需公开访问，需要在云开发控制台设置存储权限'
        ],
        troubleshooting: [
          '如果getTempFileURL返回STORAGE_FILE_NONEXIST，说明文件不存在',
          '检查文件上传是否成功',
          '检查文件路径是否正确',
          '检查云开发环境是否正确'
        ]
      }

      // 测试一个具体的文件URL
      const testFileUrl = 'cloud://cloud1-8gm001v7fd56ff43.636c-cloud1-8gm001v7fd56ff43-1372478287/uploads/document-1754535153207.pdf'

      try {
        const testResult = await uploadService.app.getTempFileURL({
          fileList: [{
            fileID: testFileUrl,
            maxAge: 60
          }]
        })

        configInfo.test_result = {
          test_file: testFileUrl,
          success: testResult.fileList?.[0]?.code === 'SUCCESS',
          code: testResult.fileList?.[0]?.code,
          message: testResult.fileList?.[0]?.message,
          full_result: testResult
        }
      } catch (testError) {
        configInfo.test_result = {
          test_file: testFileUrl,
          success: false,
          error: testError.message
        }
      }

      res.json({
        success: true,
        data: configInfo
      })
    } catch (error) {
      res.status(500).json({
        success: false,
        error: error.message
      })
    }
  }

  // 修复测试数据的文件URL
  async fixTestData(req, res) {
    try {
      // 上传一个真实的测试文件
      const testContent = `这是一个测试PDF文件
K12教育资源管理系统
文件下载测试

创建时间: ${new Date().toLocaleString()}
环境: ${process.env.CLOUDBASE_ENV}
`

      // 创建一个简单的文本文件作为测试
      const fs = require('fs')
      const path = require('path')
      const tempFile = path.join(__dirname, '../../temp/test-document.txt')

      // 确保temp目录存在
      const tempDir = path.dirname(tempFile)
      if (!fs.existsSync(tempDir)) {
        fs.mkdirSync(tempDir, { recursive: true })
      }

      fs.writeFileSync(tempFile, testContent)

      // 上传到云存储
      const uploadResult = await uploadService.app.uploadFile({
        cloudPath: 'uploads/test-document.txt',
        fileContent: fs.readFileSync(tempFile)
      })

      console.log('测试文件上传结果:', uploadResult)

      if (uploadResult.fileID) {
        // 更新一个测试文件记录
        const updateResult = await uploadService.filesCollection
          .where({ title: { $regex: '二年级数学下册应用题专项练习' } })
          .update({
            file_url: uploadResult.fileID,
            file_type: 'txt',
            file_size: testContent.length
          })

        console.log('数据库更新结果:', updateResult)

        // 清理临时文件
        fs.unlinkSync(tempFile)

        res.json({
          success: true,
          data: {
            message: '测试数据修复完成',
            uploadResult: uploadResult,
            updateResult: updateResult,
            testFileId: uploadResult.fileID
          }
        })
      } else {
        res.json({
          success: false,
          error: '文件上传失败'
        })
      }
    } catch (error) {
      console.error('修复测试数据失败:', error)
      res.status(500).json({
        success: false,
        error: error.message
      })
    }
  }
}

// 创建控制器实例
const fileController = new FileController()

// 导出控制器方法和multer中间件
module.exports = {
  // 文件管理方法
  getFileList: fileController.getFileList.bind(fileController),
  getFileDetail: fileController.getFileDetail.bind(fileController),
  updateFile: fileController.updateFile.bind(fileController),
  deleteFile: fileController.deleteFile.bind(fileController),
  batchDeleteFiles: fileController.batchDeleteFiles.bind(fileController),
  batchUpdateStatus: fileController.batchUpdateStatus.bind(fileController),
  uploadFile: fileController.uploadFile.bind(fileController),
  batchUploadFiles: fileController.batchUploadFiles.bind(fileController),
  getDownloadUrl: fileController.getDownloadUrl.bind(fileController),
  getFileStats: fileController.getFileStats.bind(fileController),
  getFilterOptions: fileController.getFilterOptions.bind(fileController),
  downloadCsvTemplate: fileController.downloadCsvTemplate.bind(fileController),
  csvBatchUpload: fileController.csvBatchUpload.bind(fileController),
  diagnoseFile: fileController.diagnoseFile.bind(fileController),
  checkStorageConfig: fileController.checkStorageConfig.bind(fileController),
  fixTestData: fileController.fixTestData.bind(fileController),

  // multer中间件
  uploadSingle: upload.single('file'),
  uploadMultiple: upload.array('files', 20), // 最多20个文件
  uploadCsv: upload.single('csvFile')
}
