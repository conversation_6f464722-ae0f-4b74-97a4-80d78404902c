// 简化的JSON上传测试
const FormData = require('form-data');
const fs = require('fs');
const { default: fetch } = require('node-fetch');

// 创建简单的测试JSON数据
const testData = [
  {
    "file_path": "C:\\Users\\<USER>\\Desktop\\test1.pdf",
    "title": "测试文件1",
    "description": "这是第一个测试文件",
    "category": "regular",
    "grade": "一年级",
    "subject": "数学",
    "volume": "上册",
    "section": "单元同步",
    "tags": ["测试"],
    "features": ["测试"],
    "status": "active"
  }
];

async function testJsonUpload() {
  console.log('=== 简化JSON上传测试 ===\n');
  
  // 检查后端服务
  try {
    console.log('1. 检查后端服务状态...');
    const healthCheck = await fetch('http://localhost:8081/api/files/stats');
    if (healthCheck.ok) {
      console.log('✅ 后端服务正常运行');
    } else {
      console.log('❌ 后端服务响应异常:', healthCheck.status);
      return;
    }
  } catch (error) {
    console.log('❌ 无法连接到后端服务，请确保后端已启动');
    console.log('启动命令: cd k12-admin/backend && npm run dev');
    return;
  }
  
  // 创建测试文件
  const testFilePath = './test-simple.json';
  fs.writeFileSync(testFilePath, JSON.stringify(testData, null, 2));
  console.log('✅ 测试JSON文件已创建');
  
  try {
    // 测试验证功能
    console.log('\n2. 测试JSON验证...');
    const validateForm = new FormData();
    validateForm.append('jsonFile', fs.createReadStream(testFilePath));
    validateForm.append('validateOnly', 'true');

    const validateResponse = await fetch('http://localhost:8081/api/files/batch/upload', {
      method: 'POST',
      body: validateForm,
      timeout: 30000  // 30秒超时
    });

    if (!validateResponse.ok) {
      console.log('❌ 验证请求失败:', validateResponse.status, validateResponse.statusText);
      const errorText = await validateResponse.text();
      console.log('错误响应:', errorText);
      return;
    }

    const validateResult = await validateResponse.json();
    console.log('验证结果:', JSON.stringify(validateResult, null, 2));
    
    if (!validateResult.success) {
      console.log('❌ JSON验证失败:', validateResult.error);
      return;
    }
    
    console.log('✅ JSON验证通过');
    
    // 测试上传功能
    console.log('\n3. 测试JSON上传...');
    const uploadForm = new FormData();
    uploadForm.append('jsonFile', fs.createReadStream(testFilePath));
    uploadForm.append('validateOnly', 'false');

    const uploadResponse = await fetch('http://localhost:8081/api/files/batch/upload', {
      method: 'POST',
      body: uploadForm,
      timeout: 60000  // 60秒超时
    });

    if (!uploadResponse.ok) {
      console.log('❌ 上传请求失败:', uploadResponse.status, uploadResponse.statusText);
      const errorText = await uploadResponse.text();
      console.log('错误响应:', errorText);
      return;
    }

    const uploadResult = await uploadResponse.json();
    console.log('上传结果:', JSON.stringify(uploadResult, null, 2));
    
    if (uploadResult.success) {
      console.log('✅ JSON上传成功！');
      console.log('成功上传:', uploadResult.successCount, '条记录');
      if (uploadResult.failedCount > 0) {
        console.log('失败记录:', uploadResult.failedCount, '条');
      }
    } else {
      console.log('❌ JSON上传失败:', uploadResult.error);
    }
    
  } catch (error) {
    console.error('❌ 测试过程中发生错误:', error.message);
    console.error('错误类型:', error.name);
    console.error('错误代码:', error.code);
    
    if (error.code === 'ECONNRESET') {
      console.log('\n💡 连接被重置，可能的原因:');
      console.log('1. 后端服务在处理过程中崩溃或重启');
      console.log('2. 云数据库连接超时或失败');
      console.log('3. 网络连接不稳定');
      console.log('\n建议:');
      console.log('1. 检查后端服务日志');
      console.log('2. 运行云数据库连接测试: node test-cloudbase-connection.js');
      console.log('3. 检查网络连接');
    }
    
  } finally {
    // 清理测试文件
    try {
      fs.unlinkSync(testFilePath);
      console.log('✅ 测试文件已清理');
    } catch (cleanupError) {
      console.warn('清理测试文件失败:', cleanupError.message);
    }
  }
}

// 运行测试
testJsonUpload();
