// 简化的HTTP测试
const { default: fetch } = require('node-fetch');

async function testHttpEndpoint() {
  console.log('=== HTTP端点测试 ===\n');

  try {
    // 测试基本的API端点
    console.log('1. 测试文件统计API...');
    const statsResponse = await fetch('http://localhost:8081/api/files/stats');
    if (statsResponse.ok) {
      const stats = await statsResponse.json();
      console.log('✅ 文件统计API正常:', stats);
    } else {
      console.log('❌ 文件统计API异常:', statsResponse.status);
      return;
    }

    // 测试文件列表API
    console.log('\n2. 测试文件列表API...');
    const listResponse = await fetch('http://localhost:8081/api/files?page=1&limit=5');
    if (listResponse.ok) {
      const list = await listResponse.json();
      console.log('✅ 文件列表API正常，文件数量:', list.data?.length || 0);
    } else {
      console.log('❌ 文件列表API异常:', listResponse.status);
      const errorText = await listResponse.text();
      console.log('错误详情:', errorText);
      // 继续测试其他API
    }

    // 测试筛选选项API
    console.log('\n3. 测试筛选选项API...');
    const optionsResponse = await fetch('http://localhost:8081/api/files/filter-options');
    if (optionsResponse.ok) {
      const options = await optionsResponse.json();
      console.log('✅ 筛选选项API正常');
    } else {
      console.log('❌ 筛选选项API异常:', optionsResponse.status);
      return;
    }

    console.log('\n✅ 所有基础API测试通过');
    console.log('问题可能出现在文件上传的multer中间件或FormData处理上');

  } catch (error) {
    console.error('❌ HTTP测试失败:', error.message);
    console.error('错误代码:', error.code);
  }
}

// 运行测试
testHttpEndpoint();
