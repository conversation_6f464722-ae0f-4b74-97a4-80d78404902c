// CSV批量上传服务
const fs = require('fs')
const path = require('path')
const csvParser = require('csv-parser')
const csvWriter = require('csv-writer')
const uploadService = require('./uploadService')

class CsvService {
  constructor() {
    // CSV模板字段定义（17个字段）
    this.csvTemplate = [
      { id: 'file_path', title: '文件路径' },
      { id: 'title', title: '标题' },
      { id: 'description', title: '描述' },
      { id: 'grade', title: '年级' },
      { id: 'subject', title: '科目' },
      { id: 'volume', title: '册别' },
      { id: 'section', title: '板块' },
      { id: 'category', title: '分类' },
      { id: 'tags', title: '标签' },
      { id: 'features', title: '文件特征' },
      { id: 'status', title: '状态' },
      { id: 'pages', title: '页数' },
      { id: 'sort_order', title: '排序权重' },
      { id: 'ad_required_count', title: '广告次数' },
      { id: 'download_count', title: '下载次数' },
      { id: 'view_count', title: '查看次数' },
      { id: 'share_count', title: '分享次数' }
    ]

    // 必填字段
    this.requiredFields = ['file_path', 'title', 'subject', 'volume', 'section']

    // 字段验证规则
    this.fieldValidations = {
      grade: ['幼升小', '一年级', '二年级', '三年级', '四年级', '五年级', '六年级', '小升初'],
      subject: ['语文', '数学', '英语', '科学', '音乐', '美术', '体育'],
      volume: ['上册', '下册', '全册'],
      category: ['regular', 'upgrade'],
      status: ['active', 'inactive'],
      section: [
        '单元同步', '单元知识点', '核心知识点', '试卷', '专项练习',
        '拼音启蒙', '认识数字', '习惯养成', '学科启蒙', '知识科普',
        '语文冲刺', '数学冲刺', '英语强化', '真题模拟', '面试准备'
      ],
      features: [
        '高清版', '彩色版', '黑白版', '可打印', '含答案', '含解析',
        '教师版', '学生版', '完整版', '精简版', '重点标注',
        '配套音频', '配套视频', '互动练习', '思维导图'
      ]
    }

    // 默认值配置
    this.defaultValues = {
      category: 'regular',
      status: 'active',
      sort_order: 0,
      ad_required_count: 1,
      download_count: 0,
      view_count: 0,
      share_count: 0,
      pages: null,
      description: '',
      grade: null,
      tags: [],
      features: []
    }

    // 中英文字段映射
    this.fieldMapping = {
      '文件路径': 'file_path',
      '标题': 'title',
      '描述': 'description',
      '年级': 'grade',
      '科目': 'subject',
      '册别': 'volume',
      '板块': 'section',
      '分类': 'category',
      '标签': 'tags',
      '文件特征': 'features',
      '状态': 'status',
      '页数': 'pages',
      '排序权重': 'sort_order',
      '广告次数': 'ad_required_count',
      '下载次数': 'download_count',
      '查看次数': 'view_count',
      '分享次数': 'share_count',
      // 添加可能的变体
      'file_path': 'file_path',
      'title': 'title',
      'description': 'description',
      'grade': 'grade',
      'subject': 'subject',
      'volume': 'volume',
      'section': 'section',
      'category': 'category',
      'tags': 'tags',
      'features': 'features',
      'status': 'status',
      'pages': 'pages',
      'sort_order': 'sort_order',
      'ad_required_count': 'ad_required_count',
      'download_count': 'download_count',
      'view_count': 'view_count',
      'share_count': 'share_count'
    }
  }

  // 生成CSV模板文件
  async generateTemplate(outputPath) {
    try {
      const writer = csvWriter.createObjectCsvWriter({
        path: outputPath,
        header: this.csvTemplate,
        encoding: 'utf8'
      })

      // 写入示例数据
      const sampleData = [{
        file_path: 'D:\\files\\一年级数学.pdf',
        title: '一年级数学练习册',
        description: '数学基础练习题集',
        grade: '一年级',
        subject: '数学',
        volume: '上册',
        section: '单元同步',
        category: 'regular',
        tags: '基础练习',
        features: '高清版,可打印,含答案',
        status: 'active',
        pages: '', // PDF文件页数自动获取，可留空
        sort_order: '100',
        ad_required_count: '1',
        download_count: '0',
        view_count: '0',
        share_count: '0'
      }, {
        file_path: 'D:\\files\\二年级语文.docx',
        title: '二年级语文教案',
        description: '语文教学教案',
        grade: '二年级',
        subject: '语文',
        volume: '下册',
        section: '专项练习',
        category: 'regular',
        tags: '教学资料',
        features: '教师版,完整版',
        status: 'active',
        pages: '15', // 非PDF文件需要手动填写页数
        sort_order: '200',
        ad_required_count: '1',
        download_count: '0',
        view_count: '0',
        share_count: '0'
      }]

      await writer.writeRecords(sampleData)

      return {
        success: true,
        message: 'CSV模板生成成功',
        path: outputPath
      }
    } catch (error) {
      console.error('生成CSV模板失败:', error)
      return {
        success: false,
        error: error.message
      }
    }
  }

  // 检测文件编码
  detectFileEncoding(filePath) {
    try {
      const buffer = fs.readFileSync(filePath)

      // 检查BOM
      if (buffer.length >= 3 && buffer[0] === 0xEF && buffer[1] === 0xBB && buffer[2] === 0xBF) {
        return 'utf8'
      }

      // 检查是否为UTF-8
      const utf8String = buffer.toString('utf8')
      if (Buffer.from(utf8String, 'utf8').equals(buffer)) {
        return 'utf8'
      }

      // 尝试GBK编码（中文Windows常用）
      return 'gbk'
    } catch (error) {
      console.warn('编码检测失败，使用默认UTF-8:', error)
      return 'utf8'
    }
  }

  // 解析CSV文件
  async parseCsvFile(csvFilePath) {
    return new Promise((resolve, reject) => {
      const results = []
      const errors = []

      console.log('开始解析CSV文件:', csvFilePath)

      // 检测文件编码
      const encoding = this.detectFileEncoding(csvFilePath)
      console.log('检测到文件编码:', encoding)

      let stream
      if (encoding === 'gbk') {
        // 使用iconv-lite处理GBK编码
        const iconv = require('iconv-lite')
        stream = fs.createReadStream(csvFilePath)
          .pipe(iconv.decodeStream('gbk'))
          .pipe(iconv.encodeStream('utf8'))
      } else {
        stream = fs.createReadStream(csvFilePath, { encoding: 'utf8' })
      }

      stream
        .pipe(csvParser({
          skipEmptyLines: true,
          skipLinesWithError: false,
          separator: ',',
          quote: '"',
          escape: '"',
          trim: true,
          bom: true // 处理BOM字符
        }))
        .on('data', (data) => {
          console.log('解析到数据行:', data)
          console.log('数据行字段名:', Object.keys(data))

          // 转换中文字段名为英文字段名
          const mappedData = {}
          Object.keys(data).forEach(chineseKey => {
            // 清理字段名：去除空格、BOM字符等
            const cleanKey = chineseKey.trim().replace(/^\uFEFF/, '').replace(/\s+/g, '')
            const englishKey = this.fieldMapping[cleanKey] || this.fieldMapping[chineseKey.trim()] || cleanKey
            const value = data[chineseKey] ? data[chineseKey].toString().trim() : ''
            mappedData[englishKey] = value

            if (this.fieldMapping[cleanKey] || this.fieldMapping[chineseKey.trim()]) {
              console.log(`字段映射: "${chineseKey}" -> "${englishKey}" = "${value}"`)
            } else {
              console.log(`未找到映射: "${chineseKey}" (清理后: "${cleanKey}")`)
            }
          })

          console.log('映射后数据:', mappedData)
          results.push(mappedData)
        })
        .on('end', () => {
          console.log(`CSV解析完成，共解析${results.length}行数据`)
          resolve({
            success: true,
            data: results,
            errors
          })
        })
        .on('error', (error) => {
          console.error('CSV解析错误:', error)
          reject({
            success: false,
            error: error.message
          })
        })
    })
  }

  // 获取PDF页数（智能页数获取策略1）
  async getPdfPageCount(filePath) {
    try {
      const pdfPoppler = require('pdf-poppler')
      const pdfInfo = await pdfPoppler.info(filePath)
      return parseInt(pdfInfo.pages) || null
    } catch (error) {
      console.warn('获取PDF页数失败:', error)
      return null
    }
  }

  // 智能页数处理（策略1：PDF自动获取，其他文件使用CSV值）
  async processFilePages(filePath, csvPages, fileExtension) {
    if (fileExtension === 'pdf') {
      // PDF文件：程序自动获取，忽略CSV值
      const autoPages = await this.getPdfPageCount(filePath)
      if (csvPages && csvPages !== autoPages) {
        console.log(`📄 PDF文件自动获取页数: ${autoPages}，忽略CSV页数: ${csvPages}`)
      }
      return autoPages
    } else {
      // 其他文件：使用CSV值
      const pages = csvPages ? parseInt(csvPages) : null
      if (pages) {
        console.log(`📄 ${fileExtension.toUpperCase()}文件使用CSV页数: ${pages}`)
      }
      return pages
    }
  }

  // 应用默认值
  applyDefaultValues(csvRow) {
    const processedRow = { ...csvRow }

    Object.keys(this.defaultValues).forEach(field => {
      if (!processedRow[field] || processedRow[field].trim() === '') {
        processedRow[field] = this.defaultValues[field]
      }
    })

    return processedRow
  }

  // 验证CSV数据
  validateCsvData(data, validateOnly = false) {
    const errors = []
    const validData = []

    data.forEach((row, index) => {
      const rowErrors = []
      const rowNumber = index + 2 // CSV行号（包含标题行）

      // 检查必填字段
      this.requiredFields.forEach(field => {
        if (!row[field] || row[field].trim() === '') {
          rowErrors.push(`第${rowNumber}行: ${field} 字段不能为空`)
        }
      })

      // 检查文件路径是否存在（仅在非验证模式下检查）
      if (row.file_path && !validateOnly && !fs.existsSync(row.file_path)) {
        rowErrors.push(`第${rowNumber}行: 文件路径不存在 - ${row.file_path}`)
      }

      // 验证枚举字段值
      Object.keys(this.fieldValidations).forEach(field => {
        if (row[field] && row[field].trim() !== '') {
          const value = row[field].trim()
          if (field === 'features' || field === 'tags') {
            // 多值字段验证
            const values = value.split(',').map(v => v.trim())
            if (field === 'features') {
              const invalidFeatures = values.filter(v => !this.fieldValidations.features.includes(v))
              if (invalidFeatures.length > 0) {
                rowErrors.push(`第${rowNumber}行: 无效的文件特征 - ${invalidFeatures.join(', ')}`)
              }
            }
          } else {
            // 单值字段验证
            if (!this.fieldValidations[field].includes(value)) {
              rowErrors.push(`第${rowNumber}行: ${field} 字段值无效 - ${value}`)
            }
          }
        }
      })

      // 验证数字字段
      const numberFields = ['pages', 'sort_order', 'ad_required_count', 'download_count', 'view_count', 'share_count']
      numberFields.forEach(field => {
        if (row[field] && row[field].trim() !== '') {
          const value = parseInt(row[field])
          if (isNaN(value) || value < 0) {
            rowErrors.push(`第${rowNumber}行: ${field} 必须为非负整数`)
          }
        }
      })

      if (rowErrors.length > 0) {
        errors.push(...rowErrors)
      } else {
        // 应用默认值
        const processedRow = this.applyDefaultValues(row)

        // 处理数组字段
        if (processedRow.tags && typeof processedRow.tags === 'string') {
          processedRow.tags = processedRow.tags.split(',').map(tag => tag.trim()).filter(tag => tag)
        }

        if (processedRow.features && typeof processedRow.features === 'string') {
          processedRow.features = processedRow.features.split(',').map(feature => feature.trim()).filter(feature => feature)
        }

        // 转换数字字段
        const numberFields = ['pages', 'sort_order', 'ad_required_count', 'download_count', 'view_count', 'share_count']
        numberFields.forEach(field => {
          if (processedRow[field] && processedRow[field] !== '') {
            processedRow[field] = parseInt(processedRow[field]) || this.defaultValues[field] || 0
          } else {
            processedRow[field] = this.defaultValues[field] || 0
          }
        })

        validData.push(processedRow)
      }
    })

    return {
      valid: errors.length === 0,
      errors,
      validData,
      totalRows: data.length,
      validRows: validData.length,
      errorRows: errors.length
    }
  }

  // 仅验证CSV文件（不上传）
  async validateCsvFile(csvFilePath) {
    try {
      console.log('开始验证CSV文件:', csvFilePath)

      // 解析CSV文件
      const parseResult = await this.parseCsvFile(csvFilePath)
      if (!parseResult.success) {
        return parseResult
      }

      // 验证数据（验证模式，不检查文件存在性）
      const validation = this.validateCsvData(parseResult.data, true)

      return {
        success: true,
        data: {
          totalRows: validation.totalRows,
          validRows: validation.validRows,
          errorRows: validation.errorRows,
          errors: validation.errors,
          validData: validation.validData.slice(0, 10), // 只返回前10条预览
          valid: validation.valid
        }
      }
    } catch (error) {
      console.error('CSV文件验证失败:', error)
      return {
        success: false,
        error: error.message
      }
    }
  }

  // 批量处理CSV上传
  async processCsvUpload(csvFilePath, progressCallback) {
    try {
      // 解析CSV文件
      const parseResult = await this.parseCsvFile(csvFilePath)
      if (!parseResult.success) {
        return parseResult
      }

      // 验证数据
      const validation = this.validateCsvData(parseResult.data)
      if (!validation.valid) {
        return {
          success: false,
          error: '数据验证失败',
          errors: validation.errors,
          summary: {
            total: validation.totalRows,
            valid: validation.validRows,
            errors: validation.errorRows
          }
        }
      }

      // 批量上传文件
      const uploadResults = []
      const totalFiles = validation.validData.length

      for (let i = 0; i < validation.validData.length; i++) {
        const row = validation.validData[i]

        try {
          // 读取文件
          const filePath = row.file_path
          const fileStats = fs.statSync(filePath)
          const fileExtension = path.extname(filePath).toLowerCase().substring(1)

          // 构造文件对象
          const file = {
            path: filePath,
            originalname: path.basename(filePath),
            size: fileStats.size,
            mimetype: this.getMimeType(path.extname(filePath).toLowerCase())
          }

          // 智能页数处理
          const csvPages = row.pages
          const finalPages = await this.processFilePages(filePath, csvPages, fileExtension)

          // 构造元数据（包含所有17个字段）
          const metadata = {
            title: row.title,
            description: row.description || '',
            grade: row.grade || null,
            subject: row.subject,
            volume: row.volume,
            section: row.section,
            category: row.category || 'regular',
            tags: row.tags || [],
            features: row.features || [],
            status: row.status || 'active',
            pages: finalPages,
            sort_order: row.sort_order || 0,
            ad_required_count: row.ad_required_count || 1,
            download_count: row.download_count || 0,
            view_count: row.view_count || 0,
            share_count: row.share_count || 0
          }

          console.log(`处理第${i + 1}/${totalFiles}行: ${row.title}`)
          if (finalPages !== null) {
            console.log(`  页数: ${finalPages}页 (${fileExtension === 'pdf' ? '自动获取' : 'CSV指定'})`)
          }

          // 上传文件
          const uploadResult = await uploadService.uploadFile(file, metadata)

          uploadResults.push({
            row: i + 1,
            filename: file.originalname,
            title: row.title,
            pages: finalPages,
            success: uploadResult.success,
            data: uploadResult.data,
            error: uploadResult.error
          })

          // 调用进度回调
          if (progressCallback) {
            progressCallback({
              current: i + 1,
              total: totalFiles,
              percentage: Math.round(((i + 1) / totalFiles) * 100),
              currentFile: file.originalname,
              currentTitle: row.title,
              pages: finalPages,
              success: uploadResult.success
            })
          }

        } catch (error) {
          console.error(`处理第${i + 1}行失败:`, error)
          uploadResults.push({
            row: i + 1,
            filename: row.file_path,
            title: row.title,
            success: false,
            error: error.message
          })
        }
      }

      const successCount = uploadResults.filter(r => r.success).length
      const failCount = uploadResults.filter(r => !r.success).length

      return {
        success: true,
        data: {
          total: totalFiles,
          success: successCount,
          failed: failCount,
          results: uploadResults
        }
      }

    } catch (error) {
      console.error('CSV批量上传失败:', error)
      return {
        success: false,
        error: error.message
      }
    }
  }

  // 根据文件扩展名获取MIME类型
  getMimeType(extension) {
    const mimeTypes = {
      '.pdf': 'application/pdf',
      '.doc': 'application/msword',
      '.docx': 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
      '.ppt': 'application/vnd.ms-powerpoint',
      '.pptx': 'application/vnd.openxmlformats-officedocument.presentationml.presentation',
      '.xls': 'application/vnd.ms-excel',
      '.xlsx': 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
    }

    return mimeTypes[extension] || 'application/octet-stream'
  }

  // 导出文件数据为CSV
  async exportFilesToCsv(files, outputPath) {
    try {
      const writer = csvWriter.createObjectCsvWriter({
        path: outputPath,
        header: [
          { id: '_id', title: 'ID' },
          { id: 'title', title: '标题' },
          { id: 'description', title: '描述' },
          { id: 'grade', title: '年级' },
          { id: 'subject', title: '科目' },
          { id: 'volume', title: '册别' },
          { id: 'section', title: '板块' },
          { id: 'category', title: '分类' },
          { id: 'tags', title: '标签' },
          { id: 'features', title: '文件特征' },
          { id: 'status', title: '状态' },
          { id: 'pages', title: '页数' },
          { id: 'file_type', title: '文件类型' },
          { id: 'file_size', title: '文件大小' },
          { id: 'sort_order', title: '排序权重' },
          { id: 'ad_required_count', title: '广告次数' },
          { id: 'download_count', title: '下载次数' },
          { id: 'view_count', title: '查看次数' },
          { id: 'share_count', title: '分享次数' },
          { id: 'created_time', title: '创建时间' }
        ],
        encoding: 'utf8'
      })

      // 处理数据
      const csvData = files.map(file => ({
        ...file,
        tags: Array.isArray(file.tags) ? file.tags.join(',') : '',
        features: Array.isArray(file.features) ? file.features.join(',') : '',
        file_size: file.file_size ? Math.round(file.file_size / 1024) + 'KB' : '',
        created_time: file.created_time ? new Date(file.created_time).toLocaleString() : ''
      }))

      await writer.writeRecords(csvData)

      return {
        success: true,
        message: '数据导出成功',
        path: outputPath,
        count: csvData.length
      }
    } catch (error) {
      console.error('导出CSV失败:', error)
      return {
        success: false,
        error: error.message
      }
    }
  }
}

module.exports = new CsvService()
