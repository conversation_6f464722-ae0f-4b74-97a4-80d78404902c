// 云数据库连接测试脚本
require('dotenv').config();
const { db } = require('./backend/src/config/cloudbase');

async function testCloudbaseConnection() {
  console.log('=== 云数据库连接测试 ===\n');
  
  // 检查环境变量
  console.log('1. 环境变量检查:');
  console.log('CLOUDBASE_ENV:', process.env.CLOUDBASE_ENV ? '✅ 已设置' : '❌ 未设置');
  console.log('CLOUDBASE_SECRET_ID:', process.env.CLOUDBASE_SECRET_ID ? '✅ 已设置' : '❌ 未设置');
  console.log('CLOUDBASE_SECRET_KEY:', process.env.CLOUDBASE_SECRET_KEY ? '✅ 已设置' : '❌ 未设置');
  console.log();
  
  // 测试数据库连接
  console.log('2. 数据库连接测试:');
  try {
    console.log('正在连接云数据库...');
    const result = await db.collection('files').limit(1).get();
    console.log('✅ 数据库连接成功');
    console.log('查询结果:', result.data.length, '条记录');
    console.log();
  } catch (error) {
    console.error('❌ 数据库连接失败:', error.message);
    console.error('错误代码:', error.code);
    console.error('错误详情:', error);
    return false;
  }
  
  // 测试写入操作
  console.log('3. 数据库写入测试:');
  try {
    const testRecord = {
      title: '测试记录_' + Date.now(),
      description: '这是一个测试记录',
      file_url: '',
      file_path: 'test/path.pdf',
      original_name: 'test.pdf',
      file_type: 'pdf',
      file_size: 0,
      pages: null,
      preview_images: [],
      grade: '一年级',
      subject: '数学',
      volume: '上册',
      section: '测试',
      category: 'regular',
      tags: ['测试'],
      features: ['测试'],
      status: 'active',
      sort_order: 0,
      ad_required_count: 1,
      download_count: 0,
      view_count: 0,
      upload_time: new Date(),
      update_time: new Date()
    };
    
    console.log('正在写入测试记录...');
    const writeResult = await db.collection('files').add(testRecord);
    console.log('✅ 数据库写入成功');
    console.log('记录ID:', writeResult.id);
    
    // 删除测试记录
    console.log('正在删除测试记录...');
    await db.collection('files').doc(writeResult.id).remove();
    console.log('✅ 测试记录已删除');
    console.log();
    
  } catch (error) {
    console.error('❌ 数据库写入失败:', error.message);
    console.error('错误代码:', error.code);
    console.error('错误详情:', error);
    return false;
  }
  
  // 测试批量操作
  console.log('4. 批量操作测试:');
  try {
    const batchRecords = [
      {
        title: '批量测试1_' + Date.now(),
        description: '批量测试记录1',
        file_path: 'test/batch1.pdf',
        original_name: 'batch1.pdf',
        file_type: 'pdf',
        grade: '一年级',
        subject: '数学',
        volume: '上册',
        section: '测试',
        category: 'regular',
        tags: ['批量测试'],
        features: ['测试'],
        status: 'active',
        upload_time: new Date()
      },
      {
        title: '批量测试2_' + Date.now(),
        description: '批量测试记录2',
        file_path: 'test/batch2.pdf',
        original_name: 'batch2.pdf',
        file_type: 'pdf',
        grade: '二年级',
        subject: '语文',
        volume: '下册',
        section: '测试',
        category: 'regular',
        tags: ['批量测试'],
        features: ['测试'],
        status: 'active',
        upload_time: new Date()
      }
    ];
    
    console.log('正在执行批量写入...');
    const batchIds = [];
    for (let i = 0; i < batchRecords.length; i++) {
      const result = await db.collection('files').add(batchRecords[i]);
      batchIds.push(result.id);
      console.log(`记录${i + 1}写入成功，ID: ${result.id}`);
    }
    
    console.log('✅ 批量写入成功');
    
    // 清理批量测试记录
    console.log('正在清理批量测试记录...');
    for (const id of batchIds) {
      await db.collection('files').doc(id).remove();
    }
    console.log('✅ 批量测试记录已清理');
    
  } catch (error) {
    console.error('❌ 批量操作失败:', error.message);
    console.error('错误代码:', error.code);
    console.error('错误详情:', error);
    return false;
  }
  
  console.log('\n=== 所有测试通过 ===');
  console.log('云数据库连接和操作正常，JSON上传功能应该可以正常工作');
  return true;
}

// 运行测试
testCloudbaseConnection()
  .then(success => {
    if (success) {
      console.log('\n🎉 云数据库测试完成，一切正常！');
    } else {
      console.log('\n❌ 云数据库测试失败，请检查配置');
    }
    process.exit(success ? 0 : 1);
  })
  .catch(error => {
    console.error('\n💥 测试过程中发生未捕获的错误:', error);
    process.exit(1);
  });
