// JSON文件上传测试脚本
const FormData = require('form-data');
const fs = require('fs');
const path = require('path');
const { default: fetch } = require('node-fetch');

// 创建测试文件
function createTestFile(filePath, content = 'This is a test file for JSON upload.') {
  const dir = path.dirname(filePath);
  if (!fs.existsSync(dir)) {
    fs.mkdirSync(dir, { recursive: true });
  }
  fs.writeFileSync(filePath, content);
  console.log(`✅ 创建测试文件: ${filePath}`);
}

// 清理测试文件
function cleanupTestFile(filePath) {
  try {
    if (fs.existsSync(filePath)) {
      fs.unlinkSync(filePath);
      console.log(`✅ 清理测试文件: ${filePath}`);
    }
  } catch (error) {
    console.warn(`清理文件失败: ${filePath}`, error.message);
  }
}

async function testJsonFileUpload() {
  console.log('=== JSON文件上传测试 ===\n');
  
  // 创建测试文件
  const testFile1 = path.join(__dirname, 'test-files', 'test1.pdf');
  const testFile2 = path.join(__dirname, 'test-files', 'test2.pdf');
  
  createTestFile(testFile1, 'PDF content for test file 1');
  createTestFile(testFile2, 'PDF content for test file 2');
  
  // 创建JSON数据（使用绝对路径）
  const testData = [
    {
      "file_path": testFile1,
      "title": "测试PDF文件1",
      "description": "这是第一个测试PDF文件",
      "category": "regular",
      "grade": "一年级",
      "subject": "数学",
      "volume": "上册",
      "section": "单元同步",
      "tags": ["测试文件", "PDF"],
      "features": ["高清版"],
      "status": "active",
      "sort_order": 1,
      "ad_required_count": 1,
      "download_count": 0,
      "view_count": 0
    },
    {
      "file_path": testFile2,
      "title": "测试PDF文件2",
      "description": "这是第二个测试PDF文件",
      "category": "regular",
      "grade": "二年级",
      "subject": "语文",
      "volume": "下册",
      "section": "课外阅读",
      "tags": ["测试文件", "PDF", "阅读"],
      "features": ["可打印"],
      "status": "active",
      "sort_order": 2,
      "ad_required_count": 1,
      "download_count": 0,
      "view_count": 0
    }
  ];
  
  // 检查后端服务
  try {
    console.log('1. 检查后端服务状态...');
    const healthCheck = await fetch('http://localhost:8081/api/files/stats');
    if (healthCheck.ok) {
      console.log('✅ 后端服务正常运行');
    } else {
      console.log('❌ 后端服务响应异常:', healthCheck.status);
      return;
    }
  } catch (error) {
    console.log('❌ 无法连接到后端服务，请确保后端已启动');
    console.log('启动命令: cd k12-admin/backend && npm run dev');
    return;
  }
  
  // 创建JSON测试文件
  const jsonTestFile = './test-file-upload.json';
  fs.writeFileSync(jsonTestFile, JSON.stringify(testData, null, 2));
  console.log('✅ JSON测试文件已创建');
  console.log('JSON内容:', JSON.stringify(testData, null, 2));
  
  try {
    // 测试验证功能
    console.log('\n2. 测试JSON验证...');
    const validateForm = new FormData();
    validateForm.append('jsonFile', fs.createReadStream(jsonTestFile));
    validateForm.append('validateOnly', 'true');

    const validateResponse = await fetch('http://localhost:8081/api/files/batch/upload', {
      method: 'POST',
      body: validateForm,
      timeout: 30000
    });

    if (!validateResponse.ok) {
      console.log('❌ 验证请求失败:', validateResponse.status, validateResponse.statusText);
      const errorText = await validateResponse.text();
      console.log('错误响应:', errorText);
      return;
    }

    const validateResult = await validateResponse.json();
    console.log('验证结果:', JSON.stringify(validateResult, null, 2));
    
    if (!validateResult.success) {
      console.log('❌ JSON验证失败:', validateResult.error);
      return;
    }
    
    console.log('✅ JSON验证通过');
    
    // 测试文件上传功能
    console.log('\n3. 测试JSON文件上传...');
    const uploadForm = new FormData();
    uploadForm.append('jsonFile', fs.createReadStream(jsonTestFile));
    uploadForm.append('validateOnly', 'false');

    console.log('开始上传，这可能需要一些时间...');
    const uploadResponse = await fetch('http://localhost:8081/api/files/batch/upload', {
      method: 'POST',
      body: uploadForm,
      timeout: 120000  // 2分钟超时
    });

    if (!uploadResponse.ok) {
      console.log('❌ 上传请求失败:', uploadResponse.status, uploadResponse.statusText);
      const errorText = await uploadResponse.text();
      console.log('错误响应:', errorText);
      return;
    }

    const uploadResult = await uploadResponse.json();
    console.log('上传结果:', JSON.stringify(uploadResult, null, 2));
    
    if (uploadResult.success) {
      console.log('✅ JSON文件上传成功！');
      console.log('成功上传:', uploadResult.summary?.success || 0, '个文件');
      if (uploadResult.summary?.failed > 0) {
        console.log('失败文件:', uploadResult.summary.failed, '个');
        console.log('失败详情:', uploadResult.results?.filter(r => !r.success));
      }
      
      // 显示上传的文件信息
      if (uploadResult.results) {
        console.log('\n上传文件详情:');
        uploadResult.results.forEach((result, index) => {
          if (result.success) {
            console.log(`文件${index + 1}: ${result.title}`);
            console.log(`  - ID: ${result.id}`);
            console.log(`  - 云存储URL: ${result.file_url || '未知'}`);
            console.log(`  - 云存储路径: ${result.file_path || '未知'}`);
          } else {
            console.log(`文件${index + 1}: ${result.title} - 失败: ${result.error}`);
          }
        });
      }
    } else {
      console.log('❌ JSON文件上传失败:', uploadResult.error);
      if (uploadResult.details) {
        console.log('错误详情:', uploadResult.details);
      }
    }
    
  } catch (error) {
    console.error('❌ 测试过程中发生错误:', error.message);
    console.error('错误类型:', error.name);
    console.error('错误代码:', error.code);
    
    if (error.code === 'ECONNRESET') {
      console.log('\n💡 连接被重置，可能的原因:');
      console.log('1. 文件上传过程中后端服务崩溃');
      console.log('2. 文件路径不存在或无法访问');
      console.log('3. 云存储上传失败');
      console.log('4. 网络连接不稳定');
    }
    
  } finally {
    // 清理测试文件
    try {
      fs.unlinkSync(jsonTestFile);
      console.log('✅ JSON测试文件已清理');
    } catch (cleanupError) {
      console.warn('清理JSON测试文件失败:', cleanupError.message);
    }
    
    // 清理创建的测试文件
    cleanupTestFile(testFile1);
    cleanupTestFile(testFile2);
    
    // 清理测试目录
    try {
      const testDir = path.join(__dirname, 'test-files');
      if (fs.existsSync(testDir)) {
        fs.rmdirSync(testDir);
        console.log('✅ 测试目录已清理');
      }
    } catch (cleanupError) {
      console.warn('清理测试目录失败:', cleanupError.message);
    }
  }
}

// 运行测试
testJsonFileUpload();
