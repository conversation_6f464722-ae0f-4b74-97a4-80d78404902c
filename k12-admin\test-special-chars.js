// 测试特殊字符文件名的JSON解析
require('dotenv').config();
const JsonService = require('./backend/src/services/jsonService-clean');

async function testSpecialCharsJson() {
  console.log('=== 测试特殊字符文件名JSON解析 ===\n');
  
  const jsonFile = './test-special-chars.json';
  
  try {
    const jsonService = JsonService;
    
    console.log('1. 测试JSON解析（包含特殊字符文件名）...');
    const parseResult = await jsonService.parseJsonFile(jsonFile);
    console.log('解析结果:', parseResult.success ? '成功' : '失败');
    
    if (!parseResult.success) {
      console.log('❌ JSON解析失败:', parseResult.error);
      return;
    }
    
    console.log('✅ JSON解析成功，记录数:', parseResult.count);
    
    // 显示解析出的文件路径和文件名
    parseResult.data.forEach((record, index) => {
      console.log(`\n记录${index + 1}:`);
      console.log(`  标题: ${record.title}`);
      console.log(`  路径: ${record.file_path}`);
      console.log(`  文件名: ${require('path').basename(record.file_path)}`);
    });
    
    console.log('\n2. 测试数据验证...');
    const validation = jsonService.validateJsonData(parseResult.data);
    console.log('验证结果:', validation.valid ? '通过' : '失败');
    
    if (validation.valid) {
      console.log(`✅ 验证通过: ${validation.validRows}/${validation.totalRows} 条记录有效`);
    } else {
      console.log(`❌ 验证失败: ${validation.errorRows} 条记录有错误`);
      validation.errors.forEach(error => {
        console.log(`  第${error.row}行 (${error.title}): ${error.errors.join(', ')}`);
      });
    }
    
    console.log('\n3. 测试文件上传（模拟）...');
    if (validation.valid) {
      console.log('开始模拟上传过程...');
      
      for (let i = 0; i < validation.validData.length; i++) {
        const fileData = validation.validData[i];
        console.log(`\n--- 模拟上传 ${i + 1}/${validation.validData.length} ---`);
        console.log(`文件: ${fileData.title}`);
        console.log(`路径: ${fileData.file_path}`);
        
        const fs = require('fs');
        if (fs.existsSync(fileData.file_path)) {
          const stats = fs.statSync(fileData.file_path);
          console.log(`✅ 文件存在，大小: ${stats.size} bytes`);
          
          // 模拟文件对象创建
          const fileObj = jsonService.createFileObject(fileData.file_path);
          console.log(`文件对象: ${fileObj.originalname}, MIME: ${fileObj.mimetype}`);
        } else {
          console.log(`❌ 文件不存在: ${fileData.file_path}`);
        }
      }
    }
    
  } catch (error) {
    console.error('❌ 测试过程中发生错误:', error.message);
    console.error('错误堆栈:', error.stack);
  }
}

// 运行测试
testSpecialCharsJson();
