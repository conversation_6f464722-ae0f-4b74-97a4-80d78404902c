// API路径测试脚本
const { default: fetch } = require('node-fetch');

async function testApiPaths() {
  console.log('=== API路径测试 ===\n');
  
  const baseUrl = 'http://localhost:8081';
  const testPaths = [
    '/api/files/stats',
    '/api/files/filter-options', 
    '/api/feedback',
    '/api/config'
  ];
  
  for (const path of testPaths) {
    try {
      console.log(`测试路径: ${path}`);
      const response = await fetch(`${baseUrl}${path}`);
      
      if (response.ok) {
        console.log(`✅ ${path} - 状态: ${response.status}`);
      } else {
        console.log(`❌ ${path} - 状态: ${response.status}`);
      }
    } catch (error) {
      if (error.code === 'ECONNREFUSED') {
        console.log(`❌ ${path} - 后端服务未启动`);
      } else {
        console.log(`❌ ${path} - 错误: ${error.message}`);
      }
    }
  }
  
  console.log('\n=== 测试完成 ===');
  console.log('如果看到"后端服务未启动"，请先启动后端服务：');
  console.log('cd k12-admin/backend && npm run dev');
}

testApiPaths();
