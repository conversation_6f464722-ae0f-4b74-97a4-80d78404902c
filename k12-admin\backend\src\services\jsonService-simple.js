const fs = require('fs')
const path = require('path')
const uploadService = require('./uploadService')

class JsonService {
  constructor() {
    console.log('JsonService 初始化 - 彻底重写版本')
  }

  // 创建文件对象（模拟multer上传的文件）
  createFileObject(filePath) {
    const stats = fs.statSync(filePath)
    const fileName = path.basename(filePath)
    const ext = path.extname(fileName).toLowerCase()

    // MIME类型映射
    const mimeTypes = {
      '.pdf': 'application/pdf',
      '.doc': 'application/msword',
      '.docx': 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
      '.xls': 'application/vnd.ms-excel',
      '.xlsx': 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
      '.ppt': 'application/vnd.ms-powerpoint',
      '.pptx': 'application/vnd.openxmlformats-officedocument.presentationml.presentation'
    }

    return {
      path: filePath,
      originalname: fileName,
      size: stats.size,
      mimetype: mimeTypes[ext] || 'application/octet-stream'
    }
  }

  // 解析JSON文件
  async parseJsonFile(jsonFilePath) {
    try {
      console.log('开始解析JSON文件:', jsonFilePath)

      if (!fs.existsSync(jsonFilePath)) {
        return {
          success: false,
          error: 'JSON文件不存在'
        }
      }

      const fileContent = fs.readFileSync(jsonFilePath, 'utf8')
      console.log('JSON文件内容长度:', fileContent.length)

      // 尝试解析JSON
      let jsonData
      try {
        jsonData = JSON.parse(fileContent)
      } catch (parseError) {
        console.log('JSON格式错误，尝试修复Windows路径')
        // 修复Windows路径转义问题
        const fixedContent = fileContent.replace(/"file_path":\s*"([^"]*?)"/g, (match, path) => {
          if (path.includes('\\') && !path.includes('\\\\')) {
            const escapedPath = path.replace(/\\/g, '\\\\')
            console.log(`🔧 路径转义: ${path} -> ${escapedPath}`)
            return `"file_path": "${escapedPath}"`
          }
          return match
        })

        try {
          jsonData = JSON.parse(fixedContent)
          console.log('✅ JSON修复后解析成功')
        } catch (secondError) {
          return {
            success: false,
            error: 'JSON格式错误: ' + secondError.message
          }
        }
      }

      if (!Array.isArray(jsonData)) {
        return {
          success: false,
          error: 'JSON必须是数组格式'
        }
      }

      console.log('✅ JSON解析成功，记录数:', jsonData.length)
      return {
        success: true,
        data: jsonData,
        count: jsonData.length
      }

    } catch (error) {
      console.error('解析JSON文件失败:', error)
      return {
        success: false,
        error: error.message
      }
    }
  }
  // 验证JSON数据
  validateJsonData(jsonData) {
    console.log('开始验证JSON数据，记录数:', jsonData.length)

    const errors = []
    const validData = []

    for (let i = 0; i < jsonData.length; i++) {
      const row = jsonData[i]
      const rowErrors = []

      console.log(`验证第${i + 1}行: ${row.title || '未知标题'}`)

      // 基本字段验证
      if (!row.file_path) rowErrors.push('缺少file_path字段')
      if (!row.title) rowErrors.push('缺少title字段')
      if (!row.subject) rowErrors.push('缺少subject字段')
      if (!row.volume) rowErrors.push('缺少volume字段')
      if (!row.section) rowErrors.push('缺少section字段')

      // 检查文件是否存在
      if (row.file_path && !fs.existsSync(row.file_path)) {
        rowErrors.push(`文件不存在: ${row.file_path}`)
      }

      if (rowErrors.length > 0) {
        errors.push({
          row: i + 1,
          title: row.title || '未知',
          errors: rowErrors
        })
      } else {
        // 设置默认值
        const validRow = {
          ...row,
          category: row.category || 'regular',
          tags: Array.isArray(row.tags) ? row.tags : [],
          features: Array.isArray(row.features) ? row.features : [],
          status: row.status || 'active',
          sort_order: row.sort_order || 0,
          ad_required_count: row.ad_required_count || 1,
          download_count: row.download_count || 0,
          view_count: row.view_count || 0
        }
        validData.push(validRow)
      }
    }

    console.log(`验证完成: ${validData.length}/${jsonData.length} 条记录有效`)

    return {
      valid: errors.length === 0,
      errors,
      validData,
      totalRows: jsonData.length,
      validRows: validData.length,
      errorRows: errors.length
    }
  }

  // 核心方法：处理JSON批量上传
  async processJsonUpload(jsonFilePath) {
    console.log('=== 开始JSON批量上传 ===')

    try {
      // 1. 解析JSON文件
      console.log('步骤1: 解析JSON文件')
      const parseResult = await this.parseJsonFile(jsonFilePath)
      if (!parseResult.success) {
        return parseResult
      }

      // 2. 验证数据
      console.log('步骤2: 验证数据')
      const validation = this.validateJsonData(parseResult.data)
      if (!validation.valid) {
        return {
          success: false,
          error: '数据验证失败',
          errors: validation.errors
        }
      }

      // 3. 逐个上传文件
      console.log('步骤3: 开始逐个上传文件')
      const results = []
      const total = validation.validData.length

      for (let i = 0; i < validation.validData.length; i++) {
        const fileData = validation.validData[i]
        const progress = Math.round(((i + 1) / total) * 100)

        console.log(`\n--- 上传进度: ${i + 1}/${total} (${progress}%) ---`)
        console.log(`正在上传: ${fileData.title}`)
        console.log(`文件路径: ${fileData.file_path}`)

        try {
          // 检查文件是否存在
          if (!fs.existsSync(fileData.file_path)) {
            throw new Error(`文件不存在: ${fileData.file_path}`)
          }

          // 创建文件对象
          const fileObj = this.createFileObject(fileData.file_path)
          console.log(`文件信息: ${fileObj.originalname}, 大小: ${fileObj.size} bytes`)

          // 准备元数据
          const metadata = {
            title: fileData.title,
            description: fileData.description || '',
            category: fileData.category,
            grade: fileData.grade,
            subject: fileData.subject,
            volume: fileData.volume,
            section: fileData.section,
            tags: fileData.tags,
            features: fileData.features,
            ad_required_count: fileData.ad_required_count,
            sort_order: fileData.sort_order
          }

          // 调用单文件上传服务
          console.log('调用uploadService.uploadFile...')
          const uploadResult = await uploadService.uploadFile(fileObj, metadata)

          if (uploadResult.success) {
            console.log(`✅ 上传成功: ${fileData.title}`)
            results.push({
              row: i + 1,
              title: fileData.title,
              success: true,
              id: uploadResult.data._id,
              file_url: uploadResult.data.file_url,
              file_path: uploadResult.data.file_path
            })
          } else {
            console.log(`❌ 上传失败: ${fileData.title} - ${uploadResult.error}`)
            results.push({
              row: i + 1,
              title: fileData.title,
              success: false,
              error: uploadResult.error
            })
          }

        } catch (error) {
          console.error(`❌ 处理文件失败: ${fileData.title}`, error.message)
          results.push({
            row: i + 1,
            title: fileData.title,
            success: false,
            error: error.message
          })
        }
      }

      // 4. 统计结果
      const successCount = results.filter(r => r.success).length
      const failedCount = results.filter(r => !r.success).length

      console.log(`\n=== 上传完成: ${successCount}/${total} 成功 ===`)

      return {
        success: true,
        message: '批量上传完成',
        summary: {
          total: total,
          success: successCount,
          failed: failedCount
        },
        results: results
      }

    } catch (error) {
      console.error('JSON批量上传失败:', error)
      return {
        success: false,
        error: '处理失败: ' + error.message
      }
    }
  }

  // 解析JSON文件
  async parseJsonFile(jsonFilePath) {
    try {
      console.log('开始解析JSON文件:', jsonFilePath)

      if (!fs.existsSync(jsonFilePath)) {
        throw new Error('JSON文件不存在')
      }

      let fileContent = fs.readFileSync(jsonFilePath, 'utf8')
      console.log('文件内容长度:', fileContent.length)

      // 处理Windows路径转义
      try {
        JSON.parse(fileContent)
        console.log('✅ JSON格式正确')
      } catch (firstParseError) {
        console.log('❌ JSON格式错误，尝试修复Windows路径')
        fileContent = fileContent.replace(/"file_path":\s*"([^"]*?)"/g, (match, path) => {
          if (path.includes('\\') && !path.includes('\\\\')) {
            const escapedPath = path.replace(/\\/g, '\\\\')
            console.log(`🔧 路径转义: ${path} -> ${escapedPath}`)
            return `"file_path": "${escapedPath}"`
          }
          return match
        })
      }

      const jsonData = JSON.parse(fileContent)
      console.log('✅ JSON解析成功，记录数:', jsonData.length)

      return {
        success: true,
        data: jsonData,
        count: jsonData.length
      }
    } catch (error) {
      console.error('❌ JSON解析失败:', error.message)
      return {
        success: false,
        error: error.message,
        data: null
      }
    }
  }

  // 简化验证 - 只检查必需字段
  validateJsonData(data) {
    console.log('开始验证JSON数据，记录数:', data.length)

    const errors = []
    const validData = []

    data.forEach((row, index) => {
      console.log(`验证第${index + 1}行: ${row.title}`)

      // 只验证最基本的字段
      if (!row.file_path || !row.title) {
        errors.push(`第${index + 1}行: file_path 和 title 不能为空`)
        return
      }

      // 设置默认值
      const validRow = {
        file_path: row.file_path,
        title: row.title,
        description: row.description || '',
        grade: row.grade || '一年级',
        subject: row.subject || '语文',
        volume: row.volume || '上册',
        section: row.section || '其他',
        category: row.category || 'regular',
        tags: Array.isArray(row.tags) ? row.tags : [],
        features: Array.isArray(row.features) ? row.features : [],
        status: row.status || 'active',
        sort_order: parseInt(row.sort_order) || 0,
        ad_required_count: parseInt(row.ad_required_count) || 1,
        download_count: parseInt(row.download_count) || 0,
        view_count: parseInt(row.view_count) || 0
      }

      validData.push(validRow)
    })

    console.log(`验证完成: ${validData.length}/${data.length} 条记录有效`)

    return {
      valid: errors.length === 0,
      errors,
      validData,
      totalRows: data.length,
      validRows: validData.length,
      errorRows: errors.length
    }
  }

  // 验证JSON文件
  async validateJsonFile(jsonFilePath) {
    try {
      console.log('开始验证JSON文件:', jsonFilePath)

      const parseResult = await this.parseJsonFile(jsonFilePath)
      if (!parseResult.success) {
        return parseResult
      }

      const validation = this.validateJsonData(parseResult.data)

      return {
        success: validation.valid,
        message: validation.valid ? '数据验证通过' : '数据验证失败',
        errors: validation.errors,
        summary: {
          total: validation.totalRows,
          valid: validation.validRows,
          errors: validation.errorRows
        }
      }
    } catch (error) {
      console.error('验证JSON文件失败:', error)
      return {
        success: false,
        error: '验证失败: ' + error.message
      }
    }
  }

  // 处理JSON上传
  async processJsonUpload(jsonFilePath) {
    console.log('=== 开始处理JSON上传 ===')
    try {
      // 解析JSON
      const parseResult = await this.parseJsonFile(jsonFilePath)
      if (!parseResult.success) {
        return parseResult
      }

      // 验证数据
      const validation = this.validateJsonData(parseResult.data)
      if (!validation.valid) {
        return {
          success: false,
          error: '数据验证失败',
          errors: validation.errors
        }
      }

      console.log('开始保存到数据库...')

      // 测试数据库连接
      try {
        console.log('测试数据库连接...')
        const testResult = await db.collection('files').limit(1).get()
        console.log('✅ 数据库连接正常')
      } catch (dbError) {
        console.error('❌ 数据库连接失败:', dbError)
        return {
          success: false,
          error: '数据库连接失败: ' + dbError.message
        }
      }

      // 保存到数据库
      const saveResults = []
      for (let i = 0; i < validation.validData.length; i++) {
        const row = validation.validData[i]

        try {
          console.log(`处理第${i + 1}条: ${row.title}`)
          console.log(`文件路径: ${row.file_path}`)

          // 检查文件是否存在
          if (!fs.existsSync(row.file_path)) {
            throw new Error(`文件不存在: ${row.file_path}`)
          }

          // 获取文件信息
          const fileStats = fs.statSync(row.file_path)
          const fileName = path.basename(row.file_path)
          const fileExtension = path.extname(fileName).toLowerCase().substring(1) || 'pdf'

          console.log(`文件信息: 大小=${fileStats.size}, 类型=${fileExtension}`)

          // 直接使用自定义的上传逻辑，避免uploadService删除原文件
          console.log(`开始上传文件到云存储...`)
          const uploadResult = await this.uploadFileToCloud(row.file_path, fileName, fileStats.size, {
            title: row.title,
            description: row.description,
            category: row.category,
            grade: row.grade,
            subject: row.subject,
            volume: row.volume,
            section: row.section,
            tags: row.tags,
            features: row.features,
            status: row.status,
            sort_order: row.sort_order,
            ad_required_count: row.ad_required_count
          })

          if (!uploadResult.success) {
            throw new Error(`文件上传失败: ${uploadResult.error}`)
          }

          console.log(`✅ 第${i + 1}条上传成功，ID:`, uploadResult.data.id)

          saveResults.push({
            row: i + 1,
            title: row.title,
            success: true,
            id: uploadResult.data.id,
            file_url: uploadResult.data.file_url,
            file_path: uploadResult.data.file_path
          })
        } catch (error) {
          console.error(`❌ 第${i + 1}条保存失败:`, error)
          console.error('错误详情:', {
            message: error.message,
            code: error.code,
            name: error.name,
            stack: error.stack
          })

          // 检查是否是网络/连接问题
          let errorMessage = error.message
          if (error.code === 'ENOTFOUND' || error.code === 'ECONNREFUSED') {
            errorMessage = '数据库连接失败，请检查网络连接'
          } else if (error.code === 'ETIMEDOUT') {
            errorMessage = '数据库操作超时，请稍后重试'
          } else if (error.message && error.message.includes('network')) {
            errorMessage = '网络连接异常，请检查网络状态'
          }

          saveResults.push({
            row: i + 1,
            title: row.title,
            success: false,
            error: errorMessage
          })
        }
      }

      const successCount = saveResults.filter(r => r.success).length
      console.log(`=== 上传完成: ${successCount}/${saveResults.length} 条成功 ===`)

      return {
        success: true,
        message: `批量上传完成`,
        summary: {
          total: saveResults.length,
          success: successCount,
          failed: saveResults.length - successCount
        },
        results: saveResults
      }
    } catch (error) {
      console.error('=== 处理JSON上传失败 ===', error)
      return {
        success: false,
        error: '处理失败: ' + error.message
      }
    }
  }

  // 验证JSON文件（不上传）
  async validateJsonFile(jsonFilePath) {
    console.log('=== 开始验证JSON文件 ===')

    try {
      // 解析JSON
      const parseResult = await this.parseJsonFile(jsonFilePath)
      if (!parseResult.success) {
        return parseResult
      }

      // 验证数据
      const validation = this.validateJsonData(parseResult.data)

      if (validation.valid) {
        return {
          success: true,
          message: '数据验证通过',
          errors: [],
          summary: {
            total: validation.totalRows,
            valid: validation.validRows,
            errors: validation.errorRows
          }
        }
      } else {
        return {
          success: false,
          message: '数据验证失败',
          errors: validation.errors,
          summary: {
            total: validation.totalRows,
            valid: validation.validRows,
            errors: validation.errorRows
          }
        }
      }

    } catch (error) {
      console.error('验证JSON文件失败:', error)
      return {
        success: false,
        error: '验证失败: ' + error.message
      }
    }
  }
}

module.exports = new JsonService()

module.exports = new JsonService()
