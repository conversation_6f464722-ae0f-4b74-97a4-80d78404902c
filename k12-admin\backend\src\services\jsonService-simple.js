const fs = require('fs')
const path = require('path')
const { db } = require('../config/cloudbase')
const uploadService = require('./uploadService')
const previewService = require('./previewService')

class JsonService {
  constructor() {
    console.log('JsonService 初始化 - 简化版本')
  }

  // 根据文件扩展名获取MIME类型
  getMimeType(extension) {
    const mimeTypes = {
      'pdf': 'application/pdf',
      'doc': 'application/msword',
      'docx': 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
      'xls': 'application/vnd.ms-excel',
      'xlsx': 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
      'ppt': 'application/vnd.ms-powerpoint',
      'pptx': 'application/vnd.openxmlformats-officedocument.presentationml.presentation',
      'jpg': 'image/jpeg',
      'jpeg': 'image/jpeg',
      'png': 'image/png',
      'gif': 'image/gif',
      'txt': 'text/plain'
    }
    return mimeTypes[extension.toLowerCase()] || 'application/octet-stream'
  }

  // 自定义文件上传到云存储的方法
  async uploadFileToCloud(filePath, fileName, fileSize, metadata) {
    try {
      const { app } = require('../config/cloudbase')

      // 生成云存储路径
      const timestamp = Date.now()
      const dateFolder = new Date().toISOString().slice(0, 10).replace(/-/g, '')
      const fileExtension = path.extname(fileName).toLowerCase().substring(1)
      const cloudFileName = `${path.basename(fileName, path.extname(fileName))}_${timestamp}.${fileExtension}`
      const cloudPath = `files/${dateFolder}/${cloudFileName}`

      console.log('云存储上传信息:', {
        原文件路径: filePath,
        云存储路径: cloudPath,
        文件大小: fileSize
      })

      // 上传到云存储
      const fileStream = fs.createReadStream(filePath)
      const uploadResult = await app.uploadFile({
        cloudPath,
        fileContent: fileStream
      })

      console.log('云存储上传成功:', uploadResult.fileID)

      // 准备数据库记录
      const fileRecord = {
        title: metadata.title,
        description: metadata.description || '',
        file_url: uploadResult.fileID,
        file_path: cloudPath,
        original_name: fileName,
        file_type: fileExtension,
        file_size: fileSize,
        pages: null,
        preview_images: [],
        grade: metadata.grade,
        subject: metadata.subject,
        volume: metadata.volume,
        section: metadata.section,
        category: metadata.category,
        tags: metadata.tags || [],
        features: metadata.features || [],
        status: metadata.status || 'active',
        sort_order: metadata.sort_order || 0,
        ad_required_count: metadata.ad_required_count || 1,
        download_count: 0,
        view_count: 0,
        upload_time: new Date(),
        update_time: new Date()
      }

      // 保存到数据库
      const dbResult = await db.collection('files').add(fileRecord)
      console.log('数据库保存成功:', dbResult.id)

      return {
        success: true,
        data: {
          id: dbResult.id,
          file_url: uploadResult.fileID,
          file_path: cloudPath,
          title: metadata.title
        }
      }

    } catch (error) {
      console.error('文件上传失败:', error)
      return {
        success: false,
        error: error.message
      }
    }
  }

  // 解析JSON文件
  async parseJsonFile(jsonFilePath) {
    try {
      console.log('开始解析JSON文件:', jsonFilePath)

      if (!fs.existsSync(jsonFilePath)) {
        throw new Error('JSON文件不存在')
      }

      let fileContent = fs.readFileSync(jsonFilePath, 'utf8')
      console.log('文件内容长度:', fileContent.length)

      // 处理Windows路径转义
      try {
        JSON.parse(fileContent)
        console.log('✅ JSON格式正确')
      } catch (firstParseError) {
        console.log('❌ JSON格式错误，尝试修复Windows路径')
        fileContent = fileContent.replace(/"file_path":\s*"([^"]*?)"/g, (match, path) => {
          if (path.includes('\\') && !path.includes('\\\\')) {
            const escapedPath = path.replace(/\\/g, '\\\\')
            console.log(`🔧 路径转义: ${path} -> ${escapedPath}`)
            return `"file_path": "${escapedPath}"`
          }
          return match
        })
      }

      const jsonData = JSON.parse(fileContent)
      console.log('✅ JSON解析成功，记录数:', jsonData.length)

      return {
        success: true,
        data: jsonData,
        count: jsonData.length
      }
    } catch (error) {
      console.error('❌ JSON解析失败:', error.message)
      return {
        success: false,
        error: error.message,
        data: null
      }
    }
  }

  // 简化验证 - 只检查必需字段
  validateJsonData(data) {
    console.log('开始验证JSON数据，记录数:', data.length)

    const errors = []
    const validData = []

    data.forEach((row, index) => {
      console.log(`验证第${index + 1}行: ${row.title}`)

      // 只验证最基本的字段
      if (!row.file_path || !row.title) {
        errors.push(`第${index + 1}行: file_path 和 title 不能为空`)
        return
      }

      // 设置默认值
      const validRow = {
        file_path: row.file_path,
        title: row.title,
        description: row.description || '',
        grade: row.grade || '一年级',
        subject: row.subject || '语文',
        volume: row.volume || '上册',
        section: row.section || '其他',
        category: row.category || 'regular',
        tags: Array.isArray(row.tags) ? row.tags : [],
        features: Array.isArray(row.features) ? row.features : [],
        status: row.status || 'active',
        sort_order: parseInt(row.sort_order) || 0,
        ad_required_count: parseInt(row.ad_required_count) || 1,
        download_count: parseInt(row.download_count) || 0,
        view_count: parseInt(row.view_count) || 0
      }

      validData.push(validRow)
    })

    console.log(`验证完成: ${validData.length}/${data.length} 条记录有效`)

    return {
      valid: errors.length === 0,
      errors,
      validData,
      totalRows: data.length,
      validRows: validData.length,
      errorRows: errors.length
    }
  }

  // 验证JSON文件
  async validateJsonFile(jsonFilePath) {
    try {
      console.log('开始验证JSON文件:', jsonFilePath)

      const parseResult = await this.parseJsonFile(jsonFilePath)
      if (!parseResult.success) {
        return parseResult
      }

      const validation = this.validateJsonData(parseResult.data)

      return {
        success: validation.valid,
        message: validation.valid ? '数据验证通过' : '数据验证失败',
        errors: validation.errors,
        summary: {
          total: validation.totalRows,
          valid: validation.validRows,
          errors: validation.errorRows
        }
      }
    } catch (error) {
      console.error('验证JSON文件失败:', error)
      return {
        success: false,
        error: '验证失败: ' + error.message
      }
    }
  }

  // 处理JSON上传
  async processJsonUpload(jsonFilePath) {
    console.log('=== 开始处理JSON上传 ===')
    try {
      // 解析JSON
      const parseResult = await this.parseJsonFile(jsonFilePath)
      if (!parseResult.success) {
        return parseResult
      }

      // 验证数据
      const validation = this.validateJsonData(parseResult.data)
      if (!validation.valid) {
        return {
          success: false,
          error: '数据验证失败',
          errors: validation.errors
        }
      }

      console.log('开始保存到数据库...')

      // 测试数据库连接
      try {
        console.log('测试数据库连接...')
        const testResult = await db.collection('files').limit(1).get()
        console.log('✅ 数据库连接正常')
      } catch (dbError) {
        console.error('❌ 数据库连接失败:', dbError)
        return {
          success: false,
          error: '数据库连接失败: ' + dbError.message
        }
      }

      // 保存到数据库
      const saveResults = []
      for (let i = 0; i < validation.validData.length; i++) {
        const row = validation.validData[i]

        try {
          console.log(`处理第${i + 1}条: ${row.title}`)
          console.log(`文件路径: ${row.file_path}`)

          // 检查文件是否存在
          if (!fs.existsSync(row.file_path)) {
            throw new Error(`文件不存在: ${row.file_path}`)
          }

          // 获取文件信息
          const fileStats = fs.statSync(row.file_path)
          const fileName = path.basename(row.file_path)
          const fileExtension = path.extname(fileName).toLowerCase().substring(1) || 'pdf'

          console.log(`文件信息: 大小=${fileStats.size}, 类型=${fileExtension}`)

          // 直接使用自定义的上传逻辑，避免uploadService删除原文件
          console.log(`开始上传文件到云存储...`)
          const uploadResult = await this.uploadFileToCloud(row.file_path, fileName, fileStats.size, {
            title: row.title,
            description: row.description,
            category: row.category,
            grade: row.grade,
            subject: row.subject,
            volume: row.volume,
            section: row.section,
            tags: row.tags,
            features: row.features,
            status: row.status,
            sort_order: row.sort_order,
            ad_required_count: row.ad_required_count
          })

          if (!uploadResult.success) {
            throw new Error(`文件上传失败: ${uploadResult.error}`)
          }

          console.log(`✅ 第${i + 1}条上传成功，ID:`, uploadResult.data.id)

          saveResults.push({
            row: i + 1,
            title: row.title,
            success: true,
            id: uploadResult.data.id,
            file_url: uploadResult.data.file_url,
            file_path: uploadResult.data.file_path
          })
        } catch (error) {
          console.error(`❌ 第${i + 1}条保存失败:`, error)
          console.error('错误详情:', {
            message: error.message,
            code: error.code,
            name: error.name,
            stack: error.stack
          })

          // 检查是否是网络/连接问题
          let errorMessage = error.message
          if (error.code === 'ENOTFOUND' || error.code === 'ECONNREFUSED') {
            errorMessage = '数据库连接失败，请检查网络连接'
          } else if (error.code === 'ETIMEDOUT') {
            errorMessage = '数据库操作超时，请稍后重试'
          } else if (error.message && error.message.includes('network')) {
            errorMessage = '网络连接异常，请检查网络状态'
          }

          saveResults.push({
            row: i + 1,
            title: row.title,
            success: false,
            error: errorMessage
          })
        }
      }

      const successCount = saveResults.filter(r => r.success).length
      console.log(`=== 上传完成: ${successCount}/${saveResults.length} 条成功 ===`)

      return {
        success: true,
        message: `批量上传完成`,
        summary: {
          total: saveResults.length,
          success: successCount,
          failed: saveResults.length - successCount
        },
        results: saveResults
      }
    } catch (error) {
      console.error('=== 处理JSON上传失败 ===', error)
      return {
        success: false,
        error: '处理失败: ' + error.message
      }
    }
  }
}

module.exports = new JsonService()
