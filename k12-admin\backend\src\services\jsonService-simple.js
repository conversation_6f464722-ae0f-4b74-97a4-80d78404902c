const fs = require('fs')
const path = require('path')
const { db } = require('../config/cloudbase')

class JsonService {
  constructor() {
    console.log('JsonService 初始化 - 简化版本')
  }

  // 解析JSON文件
  async parseJsonFile(jsonFilePath) {
    try {
      console.log('开始解析JSON文件:', jsonFilePath)

      if (!fs.existsSync(jsonFilePath)) {
        throw new Error('JSON文件不存在')
      }

      let fileContent = fs.readFileSync(jsonFilePath, 'utf8')
      console.log('文件内容长度:', fileContent.length)

      // 处理Windows路径转义
      try {
        JSON.parse(fileContent)
        console.log('✅ JSON格式正确')
      } catch (firstParseError) {
        console.log('❌ JSON格式错误，尝试修复Windows路径')
        fileContent = fileContent.replace(/"file_path":\s*"([^"]*?)"/g, (match, path) => {
          if (path.includes('\\') && !path.includes('\\\\')) {
            const escapedPath = path.replace(/\\/g, '\\\\')
            console.log(`🔧 路径转义: ${path} -> ${escapedPath}`)
            return `"file_path": "${escapedPath}"`
          }
          return match
        })
      }

      const jsonData = JSON.parse(fileContent)
      console.log('✅ JSON解析成功，记录数:', jsonData.length)

      return {
        success: true,
        data: jsonData,
        count: jsonData.length
      }
    } catch (error) {
      console.error('❌ JSON解析失败:', error.message)
      return {
        success: false,
        error: error.message,
        data: null
      }
    }
  }

  // 简化验证 - 只检查必需字段
  validateJsonData(data) {
    console.log('开始验证JSON数据，记录数:', data.length)

    const errors = []
    const validData = []

    data.forEach((row, index) => {
      console.log(`验证第${index + 1}行: ${row.title}`)

      // 只验证最基本的字段
      if (!row.file_path || !row.title) {
        errors.push(`第${index + 1}行: file_path 和 title 不能为空`)
        return
      }

      // 设置默认值
      const validRow = {
        file_path: row.file_path,
        title: row.title,
        description: row.description || '',
        grade: row.grade || '一年级',
        subject: row.subject || '语文',
        volume: row.volume || '上册',
        section: row.section || '其他',
        category: row.category || 'regular',
        tags: Array.isArray(row.tags) ? row.tags : [],
        features: Array.isArray(row.features) ? row.features : [],
        status: row.status || 'active',
        sort_order: parseInt(row.sort_order) || 0,
        ad_required_count: parseInt(row.ad_required_count) || 1,
        download_count: parseInt(row.download_count) || 0,
        view_count: parseInt(row.view_count) || 0
      }

      validData.push(validRow)
    })

    console.log(`验证完成: ${validData.length}/${data.length} 条记录有效`)

    return {
      valid: errors.length === 0,
      errors,
      validData,
      totalRows: data.length,
      validRows: validData.length,
      errorRows: errors.length
    }
  }

  // 验证JSON文件
  async validateJsonFile(jsonFilePath) {
    try {
      console.log('开始验证JSON文件:', jsonFilePath)

      const parseResult = await this.parseJsonFile(jsonFilePath)
      if (!parseResult.success) {
        return parseResult
      }

      const validation = this.validateJsonData(parseResult.data)

      return {
        success: validation.valid,
        message: validation.valid ? '数据验证通过' : '数据验证失败',
        errors: validation.errors,
        summary: {
          total: validation.totalRows,
          valid: validation.validRows,
          errors: validation.errorRows
        }
      }
    } catch (error) {
      console.error('验证JSON文件失败:', error)
      return {
        success: false,
        error: '验证失败: ' + error.message
      }
    }
  }

  // 处理JSON上传
  async processJsonUpload(jsonFilePath) {
    console.log('=== 开始处理JSON上传 ===')
    try {
      // 解析JSON
      const parseResult = await this.parseJsonFile(jsonFilePath)
      if (!parseResult.success) {
        return parseResult
      }

      // 验证数据
      const validation = this.validateJsonData(parseResult.data)
      if (!validation.valid) {
        return {
          success: false,
          error: '数据验证失败',
          errors: validation.errors
        }
      }

      console.log('开始保存到数据库...')

      // 测试数据库连接
      try {
        console.log('测试数据库连接...')
        const testResult = await db.collection('files').limit(1).get()
        console.log('✅ 数据库连接正常')
      } catch (dbError) {
        console.error('❌ 数据库连接失败:', dbError)
        return {
          success: false,
          error: '数据库连接失败: ' + dbError.message
        }
      }

      // 保存到数据库
      const saveResults = []
      for (let i = 0; i < validation.validData.length; i++) {
        const row = validation.validData[i]

        try {
          console.log(`保存第${i + 1}条: ${row.title}`)

          const fileName = path.basename(row.file_path)
          const fileRecord = {
            title: row.title,
            description: row.description,
            file_url: '', // JSON模式下为空
            file_path: row.file_path,
            original_name: fileName,
            file_type: path.extname(fileName).toLowerCase().substring(1) || 'pdf',
            file_size: 0,
            pages: null,
            preview_images: [],
            grade: row.grade,
            subject: row.subject,
            volume: row.volume,
            section: row.section,
            category: row.category,
            tags: row.tags,
            features: row.features,
            status: row.status,
            sort_order: row.sort_order,
            ad_required_count: row.ad_required_count,
            download_count: row.download_count,
            view_count: row.view_count,
            upload_time: new Date(),
            update_time: new Date()
          }

          console.log('准备保存记录:', {
            title: fileRecord.title,
            grade: fileRecord.grade,
            subject: fileRecord.subject
          })

          const result = await db.collection('files').add(fileRecord)
          console.log(`✅ 第${i + 1}条保存成功，ID:`, result.id)

          saveResults.push({
            row: i + 1,
            title: row.title,
            success: true,
            id: result.id
          })
        } catch (error) {
          console.error(`❌ 第${i + 1}条保存失败:`, error)
          console.error('错误详情:', {
            message: error.message,
            code: error.code,
            name: error.name,
            stack: error.stack
          })

          // 检查是否是网络/连接问题
          let errorMessage = error.message
          if (error.code === 'ENOTFOUND' || error.code === 'ECONNREFUSED') {
            errorMessage = '数据库连接失败，请检查网络连接'
          } else if (error.code === 'ETIMEDOUT') {
            errorMessage = '数据库操作超时，请稍后重试'
          } else if (error.message && error.message.includes('network')) {
            errorMessage = '网络连接异常，请检查网络状态'
          }

          saveResults.push({
            row: i + 1,
            title: row.title,
            success: false,
            error: errorMessage
          })
        }
      }

      const successCount = saveResults.filter(r => r.success).length
      console.log(`=== 上传完成: ${successCount}/${saveResults.length} 条成功 ===`)

      return {
        success: true,
        message: `批量上传完成`,
        summary: {
          total: saveResults.length,
          success: successCount,
          failed: saveResults.length - successCount
        },
        results: saveResults
      }
    } catch (error) {
      console.error('=== 处理JSON上传失败 ===', error)
      return {
        success: false,
        error: '处理失败: ' + error.message
      }
    }
  }
}

module.exports = new JsonService()
