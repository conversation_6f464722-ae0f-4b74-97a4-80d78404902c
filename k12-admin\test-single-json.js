// 单文件JSON上传测试
const FormData = require('form-data');
const fs = require('fs');
const path = require('path');
const { default: fetch } = require('node-fetch');

async function testSingleJsonUpload() {
  console.log('=== 单文件JSON上传测试 ===\n');
  
  // 创建一个简单的测试文件
  const testFile = path.join(__dirname, 'single-test.txt');
  fs.writeFileSync(testFile, 'This is a simple test file for JSON upload.');
  console.log(`✅ 创建测试文件: ${testFile}`);
  
  // 创建简单的JSON数据
  const testData = [
    {
      "file_path": testFile,
      "title": "简单测试文件",
      "description": "这是一个简单的测试文件",
      "category": "regular",
      "grade": "一年级",
      "subject": "数学",
      "volume": "上册",
      "section": "测试",
      "tags": ["测试"],
      "features": ["测试"],
      "status": "active"
    }
  ];
  
  // 检查后端服务
  try {
    console.log('1. 检查后端服务状态...');
    const healthCheck = await fetch('http://localhost:8081/api/files/stats');
    if (healthCheck.ok) {
      console.log('✅ 后端服务正常运行');
    } else {
      console.log('❌ 后端服务响应异常:', healthCheck.status);
      return;
    }
  } catch (error) {
    console.log('❌ 无法连接到后端服务');
    return;
  }
  
  // 创建JSON测试文件
  const jsonTestFile = './test-single.json';
  fs.writeFileSync(jsonTestFile, JSON.stringify(testData, null, 2));
  console.log('✅ JSON测试文件已创建');
  
  try {
    // 只测试上传功能，跳过验证
    console.log('\n2. 测试单文件JSON上传...');
    const uploadForm = new FormData();
    uploadForm.append('jsonFile', fs.createReadStream(jsonTestFile));
    uploadForm.append('validateOnly', 'false');

    console.log('开始上传...');
    
    // 设置较短的超时时间来快速发现问题
    const uploadResponse = await fetch('http://localhost:8081/api/files/batch/upload', {
      method: 'POST',
      body: uploadForm,
      timeout: 30000  // 30秒超时
    });

    if (!uploadResponse.ok) {
      console.log('❌ 上传请求失败:', uploadResponse.status, uploadResponse.statusText);
      const errorText = await uploadResponse.text();
      console.log('错误响应:', errorText);
      return;
    }

    const uploadResult = await uploadResponse.json();
    console.log('上传结果:', JSON.stringify(uploadResult, null, 2));
    
    if (uploadResult.success) {
      console.log('✅ 单文件JSON上传成功！');
    } else {
      console.log('❌ 单文件JSON上传失败:', uploadResult.error);
    }
    
  } catch (error) {
    console.error('❌ 测试过程中发生错误:', error.message);
    console.error('错误类型:', error.name);
    console.error('错误代码:', error.code);
    
  } finally {
    // 清理测试文件
    try {
      fs.unlinkSync(jsonTestFile);
      console.log('✅ JSON测试文件已清理');
    } catch (cleanupError) {
      console.warn('清理JSON测试文件失败:', cleanupError.message);
    }
    
    try {
      fs.unlinkSync(testFile);
      console.log('✅ 测试文件已清理');
    } catch (cleanupError) {
      console.warn('清理测试文件失败:', cleanupError.message);
    }
  }
}

// 运行测试
testSingleJsonUpload();
