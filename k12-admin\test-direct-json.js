// 直接调用JsonService的测试
require('dotenv').config();
const fs = require('fs');
const path = require('path');
const JsonService = require('./backend/src/services/jsonService-clean');

async function testDirectJsonService() {
  console.log('=== 直接调用JsonService测试 ===\n');

  // 创建测试文件
  const testFile = path.join(__dirname, 'direct-test.txt');
  fs.writeFileSync(testFile, 'This is a direct test file for JsonService.');
  console.log(`✅ 创建测试文件: ${testFile}`);

  // 创建JSON数据
  const testData = [
    {
      "file_path": testFile,
      "title": "直接测试文件",
      "description": "这是一个直接测试文件",
      "category": "regular",
      "grade": "一年级",
      "subject": "数学",
      "volume": "上册",
      "section": "测试",
      "tags": ["测试"],
      "features": ["测试"],
      "status": "active"
    }
  ];

  // 创建JSON文件
  const jsonFile = './test-direct.json';
  fs.writeFileSync(jsonFile, JSON.stringify(testData, null, 2));
  console.log('✅ JSON文件已创建');

  try {
    const jsonService = JsonService; // JsonService导出的是实例，不是类

    console.log('\n1. 测试JSON解析...');
    const parseResult = await jsonService.parseJsonFile(jsonFile);
    console.log('解析结果:', parseResult);

    if (!parseResult.success) {
      console.log('❌ JSON解析失败');
      return;
    }

    console.log('\n2. 测试数据验证...');
    const validation = jsonService.validateJsonData(parseResult.data);
    console.log('验证结果:', validation);

    if (!validation.valid) {
      console.log('❌ 数据验证失败');
      return;
    }

    console.log('\n3. 测试文件上传...');
    const uploadResult = await jsonService.processJsonUpload(jsonFile);
    console.log('上传结果:', JSON.stringify(uploadResult, null, 2));

    if (uploadResult.success) {
      console.log('✅ 直接调用JsonService成功！');
    } else {
      console.log('❌ 直接调用JsonService失败:', uploadResult.error);
    }

  } catch (error) {
    console.error('❌ 测试过程中发生错误:', error.message);
    console.error('错误堆栈:', error.stack);

  } finally {
    // 清理测试文件
    try {
      fs.unlinkSync(jsonFile);
      console.log('✅ JSON文件已清理');
    } catch (cleanupError) {
      console.warn('清理JSON文件失败:', cleanupError.message);
    }

    try {
      fs.unlinkSync(testFile);
      console.log('✅ 测试文件已清理');
    } catch (cleanupError) {
      console.warn('清理测试文件失败:', cleanupError.message);
    }
  }
}

// 运行测试
testDirectJsonService();
