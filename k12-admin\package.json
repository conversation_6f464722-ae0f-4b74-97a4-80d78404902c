{"name": "k12-admin", "version": "1.0.0", "description": "K12教育资源管理后台", "scripts": {"dev": "node start.js", "start": "node start.js", "install:all": "npm install && cd backend && npm install && cd ../frontend && npm install", "check": "node check-env.js", "setup": "npm run check && npm run install:all"}, "devDependencies": {"concurrently": "^8.2.2", "npm-run-all": "^4.1.5"}, "keywords": ["k12", "education", "admin", "management"], "author": "K12 Admin Team", "license": "MIT", "dependencies": {"axios": "^1.11.0", "dotenv": "^17.2.1", "form-data": "^4.0.4", "node-fetch": "^3.3.2"}}