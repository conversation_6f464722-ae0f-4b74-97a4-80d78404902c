// JSON上传功能测试脚本
const FormData = require('form-data');
const fs = require('fs');
const { default: fetch } = require('node-fetch');

// 创建测试JSON文件
const testJsonData = [
  {
    "file_path": "C:\\Users\\<USER>\\Desktop\\test.pdf",
    "title": "【测试】一年级数学练习册",
    "description": "这是一个测试文件，用于验证JSON上传功能",
    "category": "regular",
    "grade": "一年级",
    "subject": "数学",
    "volume": "上册",
    "section": "单元同步",
    "tags": ["测试文件", "基础练习"],
    "features": ["高清版", "可打印"],
    "status": "active",
    "sort_order": 0,
    "ad_required_count": 1,
    "download_count": 0,
    "view_count": 0
  }
];

async function testJsonUpload() {
  try {
    console.log('=== JSON上传功能测试开始 ===');
    
    // 创建临时JSON文件
    const testFilePath = './test-upload.json';
    fs.writeFileSync(testFilePath, JSON.stringify(testJsonData, null, 2));
    console.log('✅ 测试JSON文件创建成功');
    
    // 测试验证功能
    console.log('\n--- 测试JSON验证功能 ---');
    const validateForm = new FormData();
    validateForm.append('jsonFile', fs.createReadStream(testFilePath));
    validateForm.append('validateOnly', 'true');

    const validateResponse = await fetch('http://localhost:8081/api/files/batch/upload', {
      method: 'POST',
      body: validateForm
    });

    const validateResult = await validateResponse.json();
    console.log('验证结果:', JSON.stringify(validateResult, null, 2));
    
    if (validateResult.success) {
      console.log('✅ JSON验证通过');
      
      // 测试上传功能
      console.log('\n--- 测试JSON上传功能 ---');
      const uploadForm = new FormData();
      uploadForm.append('jsonFile', fs.createReadStream(testFilePath));
      uploadForm.append('validateOnly', 'false');

      const uploadResponse = await fetch('http://localhost:8081/api/files/batch/upload', {
        method: 'POST',
        body: uploadForm
      });

      const uploadResult = await uploadResponse.json();
      console.log('上传结果:', JSON.stringify(uploadResult, null, 2));
      
      if (uploadResult.success) {
        console.log('✅ JSON上传成功');
      } else {
        console.log('❌ JSON上传失败:', uploadResult.error);
      }
    } else {
      console.log('❌ JSON验证失败:', validateResult.error);
    }
    
    // 清理测试文件
    fs.unlinkSync(testFilePath);
    console.log('✅ 测试文件清理完成');
    
  } catch (error) {
    console.error('❌ 测试失败:', error.message);
    console.error('错误详情:', {
      code: error.code,
      errno: error.errno,
      syscall: error.syscall
    });
  }
}

// 检查后端服务状态
async function checkBackendStatus() {
  try {
    console.log('检查后端服务状态...');
    const response = await fetch('http://localhost:8081/api/files/stats');
    console.log('✅ 后端服务正常运行');
    return true;
  } catch (error) {
    console.log('❌ 后端服务未启动或无法访问');
    console.log('请先启动后端服务: cd k12-admin/backend && npm run dev');
    return false;
  }
}

// 主函数
async function main() {
  const backendRunning = await checkBackendStatus();
  if (backendRunning) {
    await testJsonUpload();
  }
}

main();
